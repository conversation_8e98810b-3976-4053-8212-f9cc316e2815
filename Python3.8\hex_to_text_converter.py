#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16进制数据转文本工具
"""

def hex_to_text(hex_string):
    """将16进制字符串转换为ASCII文本"""
    try:
        # 移除空格和换行
        hex_string = hex_string.replace(' ', '').replace('\n', '').replace('\r', '')
        
        # 确保是偶数长度
        if len(hex_string) % 2 != 0:
            print(f"警告: 16进制字符串长度不是偶数: {len(hex_string)}")
            hex_string = hex_string[:-1]  # 移除最后一个字符
        
        # 转换为字节
        bytes_data = bytes.fromhex(hex_string)
        
        # 转换为ASCII文本
        text = bytes_data.decode('ascii', errors='ignore')
        
        return text
    except Exception as e:
        print(f"转换错误: {e}")
        return None

def analyze_chip_data(hex_string):
    """分析芯片数据格式"""
    print("=== 16进制数据分析 ===")
    print(f"原始数据: {hex_string}")
    print(f"数据长度: {len(hex_string)} 字符")
    print()
    
    # 转换为文本
    text = hex_to_text(hex_string)
    if text:
        print("=== 转换为文本 ===")
        print(f"文本内容: {repr(text)}")
        print(f"可读文本: {text}")
        print()
        
        # 分析可能的系数格式
        print("=== 分析系数格式 ===")
        lines = text.split('\n')
        for i, line in enumerate(lines):
            if line.strip():
                print(f"行 {i+1}: {line.strip()}")
    
    # 尝试按字节分析
    print("\n=== 按字节分析 ===")
    try:
        hex_clean = hex_string.replace(' ', '').replace('\n', '').replace('\r', '')
        for i in range(0, min(len(hex_clean), 32), 2):  # 只显示前16字节
            byte_hex = hex_clean[i:i+2]
            try:
                byte_val = int(byte_hex, 16)
                char = chr(byte_val) if 32 <= byte_val <= 126 else '.'
                print(f"字节 {i//2+1:2d}: 0x{byte_hex} = {byte_val:3d} = '{char}'")
            except:
                print(f"字节 {i//2+1:2d}: 0x{byte_hex} = 无效")
    except Exception as e:
        print(f"字节分析错误: {e}")

if __name__ == "__main__":
    # 您提供的数据
    hex_data = "FEFF375610493EA5FEFF37562A493EE0FC030107FEFF375606493EE3FEFF37566D493EF4FEFF37575C493E52FEFF375779493E3BFEFF37579F493E2CFEFF375688493E41FEFF37566F493E74FEFF3756DD493E84FEFF375595493E33FEFF37564B493E25FEFF37557A493DD2FEFF375596493D79FEFF3756DE493DBEFEFF37568E493DB0FEFF375664493E4B"
    
    analyze_chip_data(hex_data)
