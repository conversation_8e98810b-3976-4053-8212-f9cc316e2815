import serial.tools.list_ports
import serial
import time
import json

def detect_serial_ports():
    """检测所有可用的串口"""
    ports = serial.tools.list_ports.comports()
    
    if not ports:
        print("未检测到任何串口设备")
        return []
    
    print("检测到的串口设备:")
    for port in ports:
        print(f"端口: {port.device}")
        print(f"描述: {port.description}")
        print(f"硬件ID: {port.hwid}")
        print("-" * 30)
    
    return [port.device for port in ports]

def connect_and_read_data(port, baudrate=9600, timeout=1):
    """连接串口并读取温度压力数据"""
    try:
        ser = serial.Serial(port, baudrate, timeout=timeout)
        print(f"已连接到串口: {port}")
        
        while True:
            if ser.in_waiting > 0:
                data = ser.readline().decode('utf-8').strip()
                if data:
                    parse_sensor_data(data)
            time.sleep(0.1)
            
    except serial.SerialException as e:
        print(f"串口连接错误: {e}")
    except KeyboardInterrupt:
        print("停止读取数据")
    finally:
        if 'ser' in locals():
            ser.close()

def parse_sensor_data(data):
    """解析传感器数据"""
    try:
        # 假设数据格式为: "TEMP:25.6,PRESS:1013.2"
        if "TEMP:" in data and "PRESS:" in data:
            parts = data.split(',')
            temp = float(parts[0].split(':')[1])
            pressure = float(parts[1].split(':')[1])
            print(f"温度: {temp}°C, 压力: {pressure}hPa")
        else:
            print(f"原始数据: {data}")
    except Exception as e:
        print(f"数据解析错误: {e}, 原始数据: {data}")

if __name__ == "__main__":
    available_ports = detect_serial_ports()
    if available_ports:
        # 使用第一个可用端口
        connect_and_read_data(available_ports[0])
