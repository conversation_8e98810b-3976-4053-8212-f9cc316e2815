第1页:
 
   
      JR-3100 等相关产品标识为所有，不得随意侵犯
文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504© Copyright 20 24  
本手册 包含的 产品信息和应用说明 可能会随时变动，恕不另行通知 ，请确保
您的文档是最新版本 。 除非版权法允许，否则在未经事先书面许可的情况
下，严禁 复制、 改编、转载 或翻译本手册的内容        
 
 
 
 
 
 
 
  •本指导手册仅适用于单通道白光解调模块，支持产品的型号包括
JR-3100D， JR-3220DN 
 
芯片连接器 - JR-3220DN系列
白光干涉解调模块
通讯指南  
 
 


第2页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页1产品安全信息与阅读指南  
 △!警告 安全事项  
• 在使用本产品时，一定要遵守基本的安全注意事项以降低火灾或触电的可能性  
• 请勿将本产品 直接放在水中或任何 高度潮湿环境中安装或使用  
△!警告 触电隐患  
•  使用之前， 请仔细阅读并熟知《产品安装手册》中的所有说明 并留意产品上标
出的所有警告和说明  
• 仪器应在通风状况好的地方使用  
• 产品使用前推荐 安放在稳固的机械平台或机柜，确保安装在安全的地方  
• 产品使用时避免踩到或绊到 电源线， 造成电源线或解调仪 受损坏 
• 为设备连接 外部电源时，只使用接地 的电源插座。如果不知道插座是否有地
线，请先咨询电气技术人员  
• 必须使用原厂提供的电源适配器  
• 必须按照原厂要求的串口通讯定义方式连接外部线缆  
•  进行清洁前， 必须先拔掉产品的电源插头  
• 产品开机以后，建议进行完 5分钟的预热运行后再做相关的操作  
• 如果本产品无法正常工作，请 参考《产品安装手册》 寻求帮助 
△!警告 本说明书按“现状”提供，编排时力求内容全面正确可靠，已
经尽最大的努力避免任何的人为失误，但实际上还有如文件格式转换、分
发、传输、印刷等无法控制环节，因而不保证用户收到之时没有任何错
误。对于文档中所有明示或暗示的陈述、保证和条款，均不提供任何法律
保证；且不对任何因使用本文档的任何内容所导致的任何损失承担任何或
连带的法律责任，除非此类免责声明在法律上被视为无效。  

第3页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页2目录 
 
目录  ................................ ................................ ................................ ................................ ........................  2 
第一章  RS-232通讯控制硬件接口  ................................ ................................ ................................ ..... 3 
第二章  芯片连接器相关命令字通讯指南  ................................ ................................ ...........................  5 
2.1 串口通讯输入  ................................ ................................ ................................ ..............................  5 
2.2 串口通讯延迟  ................................ ................................ ................................ ..............................  5 
2.3 串口通讯命令行  ................................ ................................ ................................ ..........................  5 
2.3.1 命令行 总结  ................................ ................................ ................................ ..........................  5 
2.3.2 配置类  ................................ ................................ ................................ ................................ .. 5 
2.3.3 数据类  ................................ ................................ ................................ ................................ .. 6 
产品支持和咨询服务  ................................ ................................ ................................ .............................  8 
 
  

第4页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页3第一章  RS-232通讯控制硬件接口  
 
 
  J R - 3 2 20 DN系列白光干涉模块通过 RS-232接口与外部主机完
成通讯连接。 RS-232接口如下图 1.1所示3针接口，其外部匹配的连接
器可选用 Molex的50579703 或50579403 。
图 1.1  
 
 
 


第5页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页4模块RS-232接口配置参数的定义如下：  
波特率 57.6kbps   
数据位数  8位  
奇偶校验  无  
停止位 1位  
流控（握手协议）  无   
表 1.1 RS-232接口配置  
   
解调模块的 RS-232命令字目前不支持国际标准化组织推荐的可编程
仪器标准化指令集（ SCPI, standard commands for programmable 
instrumentation ）的格式和语法，采用自定义的形式。具体的命令定
义参见第二章。  
面板串行 RS232的定义如下：  
 管脚 1 RS232输出   
管脚 2 通讯接口地，  需要与
电源地同源  
管脚 3 RS232输入  
表 1.2 RS-232接口定义  
 
  
 
产品后续更新中串口命令定义可能会与当前的不保持一致。如有修改，恕不通知。  
 
 


第6页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页5第二章  芯片连接器相关命令字通讯指南  
2.1 串口通讯输入  
模块通讯串口命令行采用 16进制格式（非 ASCII码），不强制要求
采用回车键 (Enter)作为命令行终结符。  
2.2 串口通讯延迟  
串口通讯在处理命令行时，通常会有时间延迟等待和迟滞响应的现
象，因而外部接口程序发送命令后需要添加适当的延迟时间余量，以便于
解调仪能正确地响应程序发送的指令并回送数据。  
2.3 串口通讯命令行  
2.3.1  命令行总结 
类别 命令行 说明 
配置类 F0 EE 查询模块已连接的传感器芯片系数  
数据类 EE EE EE 当模块检测到传感器插入时，会主动上
报“传感器接入”状态信号  
 F1 FF 
## ...## EE 
EE ** ** ** 
** FF FF  查询模块芯片连接器系命令字发送后，
模块返回的已插入传感器系数 和CRC校
验码 
表 2.1 串口命令字总结  
2.3.2  配置类  
a. 查询模块已连接的传感器芯片系数  （F0 EE） 
向模块发送 F0 EE命令字，查询模块已连接的芯片系数，模块上报数据
详解见下文 2.3.3节b小节。  
 

第7页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页62.3.3  数据类  
a. 传感器插入状态信号  （EE EE EE ) 
当模块处于解调状态（ FA 07 00 01使能上报腔长后）检测到传感器插入
时，模块会主动上报传感器插入信号  EE EE EE（3字节 16进制数）。  
注意：如果传感器在模块处于非解调状态时插入，模块不会主动上报传
感器插入信号。  
b. 模块上报的芯片系数  （F1 FF ## ...## EE EE ** ** ** **  FF 
FF） 
 
⚫ 
 
 
⚫ 
 
⚫ 
 
 
 
 
  当芯片连接器存在系数芯片（即模块法兰中插入了传感器）时，上位机向
模块发送“ F0 EE”命令字后，模块会读取已插入传感器芯片中的系数并上
报，上报数据格式为“ F1 FF ## ...##EE EE ** ** ** ** FF FF ”，其中以“ F1 
FF ”为帧头，“ FF FF ”为帧尾，“ ## ... ##”是系数主体（最长不超过
480个字节），系数主体是多项式系数文本的 16进制 ASCII码，每个字节
代表一个字符，实际使用中将系数主体转换成 ASCII码文本即可。“ EE 
EE”为系数主体与 CRC校验码分隔符，分隔符与帧尾之间的 4个“ ** ** 
** **”字节为 CRC校验码， CRC校验码是使用 CRC-32算法对系数主体的
16进制 ASCII码进行循环冗余校验。
当芯片连接器中不存在芯片（即模块法兰中没有插入传感器）时，上位机
向模块发送 F0 EE命令字后，模块仅上报“ FF FF”，表示当前芯片连接器
未识别到系数芯片。
根据传感器类型的不同，系数主体转换成文本后，系数会有以下 3种格
式，分别为：
1）J8200 P系列传感器单个系数：
例如：当向仪表发送 F0 EE命令字后，仪表响应的 Hex内容如下图 1

第8页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页7 
图1 
将图 1中将橙色部分系数主体转换成文本后，如图 2所示：  
 
 
 
 图2
2）J8200 P系列传感器多个系数：
当传感器存在多个系数（一般指 25°C和37°C）时，系数主体转换
成文本后如下图 3所示：  
 
图3 
 3）JR-1520TP系列传感器系数：


第9页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页8JR-1520TP系列传感器系数芯片，系数主体转换成文本后如下图 4所
示：  
 
图4 
 
 
 
产品支持和咨询服务  
如果您在使用过程中发现有任何错误或有什么问题，请联系咨询我们。  
 
 
 
  


第10页:
       
 文档编号： J R - 3 2 20 DN系列-芯片连接器 -通讯指南 V0.9.9发布版本 / 202 504页9变更历史  
版本号 变更编号  更改说明  更改日期  更改者  
V0.9.7  N/A 新建  2024  王志刚  
V0.9. 8 N/A 增加 S520系列传感器系数 说明  2025 -01 王志刚  
V0.9.9  N/A 更新 2.3中模块上报数据的格式描
述，增加 CRC校验内容。  2025 -04 董立得  
 