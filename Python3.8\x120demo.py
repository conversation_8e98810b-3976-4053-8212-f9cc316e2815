
import os
import sys
from ctypes import *

# 尝试加载动态库，处理不同操作系统的情况
libx120 = None
library_loaded = False

# 尝试加载动态库的不同版本
library_files = []
if os.name == 'nt':  # Windows
    library_files = ["libx120-dev-sdk.dll", "libx120-dev-sdk.so"]
else:  # Linux/Unix
    library_files = ["./libx120-dev-sdk.so", "./libx120-dev-sdk.dll"]

for lib_file in library_files:
    try:
        libx120 = cdll.LoadLibrary(lib_file)
        library_loaded = True
        print(f"成功加载动态库: {lib_file}")
        break
    except OSError:
        continue

if not library_loaded:
    print("警告：无法加载动态库")
    print("尝试过的文件:")
    for lib_file in library_files:
        print(f"  - {lib_file}")
    print("\n请确保以下文件之一存在于当前目录中:")
    print("  - libx120-dev-sdk.dll (Windows版本)")
    print("  - libx120-dev-sdk.so (Linux版本)")
    print("\n继续运行程序进行测试（传感器功能将不可用）...")

    # 创建一个模拟的库对象用于测试
    class MockLibrary:
        pass

    libx120 = MockLibrary()

    # 添加模拟函数
    def mock_set_sensor_coefficients(data):
        print("模拟：设置传感器系数")
        return True

    def mock_is_temperature_compensation_enabled():
        print("模拟：检查温补状态")
        return True

    def mock_enable_temperature_compensation(enable):
        print(f"模拟：{'开启' if enable else '关闭'}温补")

    def mock_compute_temperature(cl1):
        print(f"模拟：计算温度，输入腔长={cl1}")
        return 25.5

    def mock_compute_pressure(cl1, cl2):
        print(f"模拟：计算压力，输入腔长1={cl1}, 腔长2={cl2}")
        return 101325.0

    def mock_calibrate_pressure(cl1, cl2):
        print(f"模拟：压力校零，输入腔长1={cl1}, 腔长2={cl2}")
        return True

    def mock_calibrate_temperature(temp, cl1):
        print(f"模拟：温度校N，输入温度={temp}, 腔长1={cl1}")
        return True

    # 设置模拟函数
    libx120.set_sensor_coefficients = mock_set_sensor_coefficients
    libx120.is_temperature_compensation_enabled = mock_is_temperature_compensation_enabled
    libx120.enable_temperature_compensation = mock_enable_temperature_compensation
    libx120.compute_temperature = mock_compute_temperature
    libx120.compute_pressure = mock_compute_pressure
    libx120.calibrate_pressure = mock_calibrate_pressure
    libx120.calibrate_temperature = mock_calibrate_temperature

    # 为模拟函数添加属性（模拟ctypes函数的属性）
    for func_name in ['set_sensor_coefficients', 'is_temperature_compensation_enabled',
                      'enable_temperature_compensation', 'compute_temperature',
                      'compute_pressure', 'calibrate_pressure', 'calibrate_temperature']:
        func = getattr(libx120, func_name)
        func.argtypes = None
        func.restype = None

    print("已启用模拟模式")

functions = [ "退出", "设置传感器系数", "查看温补状态", "设置温补开关", "计算温度", "计算压力", "压力校零", "温度校N" ]
# 显示功能界面
def info_print():
    """显示功能界面"""
    print_hint('----------')
    print_hint('请选择功能')
    print_hint('----------')
    i = 1;
    while i <= 7:
        print(f'{i}、{functions[i]}')
        i = i + 1
    print('0、退出')
    print_hint('----------')

def print_error(str):
    print(f'xxxxxxxxxx{str}xxxxxxxxxx')
    sys.stdout.flush()

def print_hint(str):
    print(f'----------{str}----------')
    sys.stdout.flush()

# 1、设置传感器系数


def call_set_sensor_coefficients(no):
    """设置传感器系数"""
    print_hint(functions[no])
    try:
        data = input("输入传感器系数：")
        if not data.strip():
            print_error("传感器系数不能为空")
            return
        c_str = c_char_p(data.encode('utf-8'))
        libx120.set_sensor_coefficients.argtypes = [c_char_p]
        libx120.set_sensor_coefficients.restype = c_bool
        flag = libx120.set_sensor_coefficients(c_str)
        if flag:
            print_hint('设置系数成功')
        else:
            print_error('设置系数失败')
    except Exception as e:
        print_error(f'设置传感器系数失败: {e}')

# 2、读取压力温度补偿开关的状态


def call_is_temperature_compensation_enabled(no):
    """读取压力温度补偿开关的状态"""
    print_hint(functions[no])
    try:
        libx120.is_temperature_compensation_enabled.restype = c_bool
        flag = libx120.is_temperature_compensation_enabled()
        if flag:
            print_hint("温补已开启")
        else:
            print_hint("温补已关闭")
    except Exception as e:
        print_error(f"读取温补状态失败: {e}")

# 3、设置压力温度补偿开关


def call_enable_temperature_compensation(no):
    """设置压力温度补偿开关"""
    print_hint(functions[no])
    try:
        choice = int(input("输入序号（0-关闭温补，1-开启温补）："))
        libx120.enable_temperature_compensation.argtypes = [c_bool]
        libx120.enable_temperature_compensation.restype = None
        if choice == 0:
            libx120.enable_temperature_compensation(False)
            print_hint('关闭温补完成')
        elif choice == 1:
            libx120.enable_temperature_compensation(True)
            print_hint('开启温补完成')
        else:
            print_error("序号无效")
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f"设置温补开关失败: {e}")

# 4、计算温度


def call_compute_temperature(no):
    """计算温度"""
    print_hint(functions[no])
    try:
        cl1 = float(input("输入通道1腔长："))
        libx120.compute_temperature.argtypes = [c_float]
        libx120.compute_temperature.restype = c_float
        temp = libx120.compute_temperature(cl1)
        print_hint(f'计算温度：{temp:.2f}°C')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'计算温度失败: {e}')

# 5、计算压力


def call_compute_pressure(no):
    """计算压力"""
    print_hint(functions[no])
    try:
        cl1 = float(input("输入通道1腔长："))
        cl2 = float(input("输入通道2腔长："))
        libx120.compute_pressure.argtypes = [c_float, c_float]
        libx120.compute_pressure.restype = c_float
        pressure = libx120.compute_pressure(cl1, cl2)
        print_hint(f'计算压力：{pressure:.2f} Pa')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'计算压力失败: {e}')

# 6、压力校零


def call_calibrate_pressure(no):
    """压力校零"""
    print_hint(functions[no])
    try:
        cl1 = float(input("输入通道1腔长："))
        cl2 = float(input("输入通道2腔长："))
        libx120.calibrate_pressure.argtypes = [c_float, c_float]
        libx120.calibrate_pressure.restype = c_bool
        flag = libx120.calibrate_pressure(cl1, cl2)
        if flag:
            print_hint('压力校零成功')
        else:
            print_error('压力校零失败')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'压力校零失败: {e}')

# 7、温度校N


def call_calibrate_temperature(no):
    """温度校N"""
    print_hint(functions[no])
    try:
        temp = float(input("输入校零温度："))
        cl1 = float(input("输入通道1腔长："))
        libx120.calibrate_temperature.argtypes = [c_float, c_float]
        libx120.calibrate_temperature.restype = c_bool
        flag = libx120.calibrate_temperature(temp, cl1)
        if flag:
            print_hint('温度校N成功')
        else:
            print_error('温度校N失败')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'温度校N失败: {e}')

# 主程序入口
def main():
    """主程序"""
    print_hint("X120传感器控制程序")
    print_hint("程序启动成功")

    while True:
        try:
            info_print()
            no = int(input("输入功能序号："))

            if no == 1:
                call_set_sensor_coefficients(1)
            elif no == 2:
                call_is_temperature_compensation_enabled(2)
            elif no == 3:
                call_enable_temperature_compensation(3)
            elif no == 4:
                call_compute_temperature(4)
            elif no == 5:
                call_compute_pressure(5)
            elif no == 6:
                call_calibrate_pressure(6)
            elif no == 7:
                call_calibrate_temperature(7)
            elif no == 0:
                print_hint("退出程序")
                break
            else:
                print_error("无效功能序号")

        except ValueError:
            print_error("请输入有效的数字")
        except KeyboardInterrupt:
            print_hint("\n程序被用户中断")
            break
        except Exception as e:
            print_error(f"程序运行出错: {e}")

        # 添加分隔线，便于查看
        print()

if __name__ == "__main__":
    main()


