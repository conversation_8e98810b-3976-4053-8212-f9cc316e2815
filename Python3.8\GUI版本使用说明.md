# X120传感器控制系统 - GUI版本使用说明

## 🎉 概述

我们为您创建了一个现代化的桌面GUI应用程序，替代了原来的终端界面。这个GUI版本提供了直观的图形界面，包含实时数据可视化、状态监控和完整的设备控制功能。

## 🚀 快速开始

### 方法1：使用启动脚本（推荐）
1. 双击运行 `启动GUI程序.bat`
2. 脚本会自动检查和安装依赖
3. GUI程序将自动启动

### 方法2：手动启动
```bash
cd Python3.8
python x120_gui.py
```

## 📱 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                X120传感器控制系统                              │
├─────────────────┬───────────────────────────────────────────┤
│   控制面板       │            数据显示                        │
│                │                                           │
│ ┌─连接状态─┐    │  ┌─实时数据─┐                             │
│ │ 未连接    │    │  │温度腔长  │ 压力腔长 │ 环境压力 │ 版本 │  │
│ └─────────┘    │  └─────────┘                             │
│                │                                           │
│ ┌─运行模式─┐    │  ┌─────────图表显示─────────┐              │
│ │○串口模式  │    │  │                        │              │
│ │●模拟模式  │    │  │    实时腔长数据曲线      │              │
│ └─────────┘    │  │                        │              │
│                │  └─────────────────────────┘              │
│ [连接设备]      │                                           │
│                │                                           │
│ ┌─基础功能─┐    │                                           │
│ │[设置系数]│    │                                           │
│ │[温补状态]│    │                                           │
│ │  ...     │    │                                           │
│ └─────────┘    │                                           │
│                │                                           │
│ ┌─高级功能─┐    │                                           │
│ │[查询版本]│    │                                           │
│ │[环境压力]│    │                                           │
│ │  ...     │    │                                           │
│ └─────────┘    │                                           │
├─────────────────┴───────────────────────────────────────────┤
│                    系统日志                                  │
│ [时间] 系统启动完成，等待连接设备...                          │
│ [时间] 模拟模式连接成功                                      │
│ [时间] 开始实时数据采集                                      │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 功能详解

### 1. 连接管理
- **连接状态显示**：实时显示设备连接状态
- **运行模式选择**：
  - 串口模式：连接真实硬件设备
  - 模拟模式：用于测试和演示
- **一键连接**：点击"连接设备"按钮即可连接

### 2. 设备控制
#### 基础功能（1-7）
- 设置传感器系数
- 查看温补状态
- 设置温补开关
- 计算温度
- 计算压力
- 压力校零
- 温度校N

#### 高级功能（8-11）
- 查询设备版本
- 查询环境压力
- 查询芯片系数
- 实时腔长数据

### 3. 数据可视化
#### 实时数据显示
- **温度通道腔长**：实时显示，单位nm
- **压力通道腔长**：实时显示，单位nm
- **环境压力**：显示当前环境压力，单位Pa
- **设备版本**：显示设备固件版本

#### 图表功能
- **实时曲线图**：显示腔长数据的时间变化
- **双通道显示**：同时显示温度和压力通道数据
- **自动缩放**：图表自动调整显示范围
- **数据保存**：支持保存图表为PNG格式

### 4. 日志系统
- **彩色日志**：不同类型的消息用不同颜色显示
  - 🟢 绿色：成功操作
  - 🟠 橙色：警告信息
  - 🔴 红色：错误信息
  - ⚫ 黑色：普通信息
- **时间戳**：每条日志都有精确的时间记录
- **自动滚动**：新日志自动滚动到可见区域
- **日志保存**：支持保存日志到文本文件

## 🎯 使用流程

### 基本使用流程
1. **启动程序** → 双击`启动GUI程序.bat`
2. **选择模式** → 选择"串口模式"或"模拟模式"
3. **连接设备** → 点击"连接设备"按钮
4. **执行功能** → 点击相应的功能按钮
5. **查看结果** → 在数据显示区域和日志中查看结果

### 实时数据采集流程
1. **确保已连接** → 设备状态显示"已连接"
2. **开始采集** → 点击"开始实时采集"按钮
3. **观察数据** → 实时数据和图表开始更新
4. **停止采集** → 点击"停止实时采集"按钮

## 📊 数据管理

### 图表操作
- **清空图表**：点击"清空图表"按钮清除所有数据点
- **保存图表**：点击"保存图表"按钮保存为PNG图片
- **自动更新**：实时采集时图表自动更新（每秒1次）
- **数据限制**：最多显示100个数据点，超出后自动删除旧数据

### 日志管理
- **清空日志**：点击"清空日志"按钮清除所有日志
- **保存日志**：点击"保存日志"按钮保存为文本文件
- **日志类型**：支持信息、成功、警告、错误四种类型

## ⚙️ 高级设置

### 串口模式配置
- 程序会自动检测可用串口
- 默认使用第一个可用串口
- 串口参数：57.6kbps, 8位数据位, 无奇偶校验, 1位停止位

### 模拟模式特点
- 生成随机的模拟数据
- 模拟真实的设备响应
- 用于程序测试和演示

## 🔍 故障排除

### 常见问题

1. **程序无法启动**
   ```
   解决方案：
   - 确保已安装Python 3.6+
   - 运行启动脚本自动安装依赖
   - 检查是否有杀毒软件阻止
   ```

2. **连接失败**
   ```
   串口模式：
   - 检查设备是否正确连接
   - 确认串口驱动已安装
   - 检查串口是否被其他程序占用
   
   模拟模式：
   - 通常不会失败，如有问题请重启程序
   ```

3. **图表不显示**
   ```
   解决方案：
   - 确保matplotlib库已正确安装
   - 重启程序
   - 检查系统图形驱动
   ```

4. **实时数据不更新**
   ```
   解决方案：
   - 确保设备已连接
   - 检查"开始实时采集"按钮状态
   - 查看日志中的错误信息
   ```

### 调试模式
如需调试，可以在命令行中运行：
```bash
python x120_gui.py
```
这样可以看到详细的错误信息。

## 🎨 界面特色

- **现代化设计**：使用ttk组件，界面美观现代
- **响应式布局**：窗口大小可调整，组件自适应
- **直观操作**：所有功能都有清晰的按钮和标签
- **实时反馈**：操作结果立即显示在界面上
- **数据可视化**：图表直观显示数据变化趋势

## 📈 性能特点

- **多线程设计**：UI响应流畅，不会卡顿
- **内存优化**：自动限制数据点数量，防止内存溢出
- **实时性能**：数据更新频率1Hz，满足实时监控需求
- **稳定性**：完善的异常处理，程序运行稳定

---

**享受您的现代化X120传感器控制系统GUI版本！** 🎉

如有任何问题或建议，请查看日志信息或联系技术支持。
