# X120 传感器控制程序

这是一个用于控制 X120 传感器的 Python 程序，通过调用动态库来实现各种传感器操作功能。

## 功能特性

程序提供以下功能：

1. **设置传感器系数** - 配置传感器的工作参数
2. **查看温补状态** - 检查温度补偿功能的开启状态
3. **设置温补开关** - 开启或关闭温度补偿功能
4. **计算温度** - 根据通道1腔长计算温度值
5. **计算压力** - 根据通道1和通道2腔长计算压力值
6. **压力校零** - 执行压力传感器的校零操作
7. **温度校N** - 执行温度传感器的校准操作

## 系统要求

- Python 3.6 或更高版本
- 对应的动态库文件：
  - Windows: `libx120-dev-sdk.dll`
  - Linux/Unix: `libx120-dev-sdk.so`

## 安装和使用

### 1. 准备动态库文件

将对应的动态库文件放在程序目录中：

```
Python3.8/
├── x120demo.py          # 主程序
├── libx120-dev-sdk.dll  # Windows 动态库
└── libx120-dev-sdk.so   # Linux 动态库 (可选)
```

**注意：** 如果没有对应的动态库文件，程序会自动启用模拟模式，所有功能都可以正常测试，但不会与实际硬件交互。

### 2. 运行程序

```bash
cd Python3.8
python x120demo.py
```

程序启动后会显示：
- 如果找到动态库：直接进入正常模式
- 如果未找到动态库：显示警告信息并进入模拟模式

### 3. 使用界面

程序启动后会显示功能菜单：

```
----------请选择功能----------
1、设置传感器系数
2、查看温补状态
3、设置温补开关
4、计算温度
5、计算压力
6、压力校零
7、温度校N
0、退出
----------
```

输入对应的数字选择功能，按照提示输入参数即可。

## 程序改进

相比原始版本，此版本包含以下改进：

### 错误处理
- 添加了动态库加载失败的处理
- 为所有用户输入添加了数据验证
- 为所有API调用添加了异常处理
- 提供了清晰的错误信息

### 用户体验
- 改进了输出格式，温度和压力值包含单位
- 添加了输入验证，防止空值和无效输入
- 支持 Ctrl+C 优雅退出
- 添加了程序启动提示

### 代码质量
- 修复了变量名拼写错误 (`funtions` -> `functions`)
- 添加了详细的函数文档
- 改进了代码结构和可读性
- 添加了类型声明以确保API调用正确

### 跨平台支持
- 自动检测操作系统并加载对应的动态库
- Windows 和 Linux 系统都支持

### 模拟模式
- 当找不到动态库文件时，自动启用模拟模式
- 模拟模式下所有功能都可以测试，返回模拟数据
- 便于程序开发和功能验证

## 测试

运行测试脚本来验证程序是否正常：

```bash
python test_x120demo.py
```

测试脚本会检查：
- 程序语法是否正确
- 所有必需的函数是否存在
- 动态库文件是否存在

## 故障排除

### 常见问题

1. **"无法加载动态库"错误**
   - 确保动态库文件在程序目录中
   - 检查文件名是否正确
   - 确保动态库与系统架构匹配（32位/64位）

2. **"请输入有效的数字"错误**
   - 确保输入的是数字格式
   - 小数点使用英文句号 (.)

3. **API调用失败**
   - 检查传感器是否正确连接
   - 确认动态库版本与传感器兼容
   - 检查输入参数是否在有效范围内

### 调试模式

如需调试，可以在程序中添加更详细的日志输出，或者使用 Python 调试器：

```bash
python -m pdb x120demo.py
```

## 许可证

请根据您的项目需求添加适当的许可证信息。

## 联系方式

如有问题或建议，请联系开发团队。
