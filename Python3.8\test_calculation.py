#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试温度压力计算
"""

def calculate_with_observed_data(temp_cavity, press_cavity):
    """基于观测数据的临时计算方法"""
    try:
        # 基于实际观测: 温度腔14165nm->26.1°C, 压力腔18750nm->1.1mmHg
        temp_ref_cavity = 14165.0
        temp_ref_value = 26.1
        temp_coefficient = 0.1  # 需要根据更多数据校准
        
        press_ref_cavity = 18750.0
        press_ref_value = 1.1
        press_coefficient = 0.01  # 需要根据更多数据校准
        
        temperature = temp_coefficient * (temp_cavity - temp_ref_cavity) + temp_ref_value
        pressure_mmhg = press_coefficient * (press_cavity - press_ref_cavity) + press_ref_value
        
        return temperature, pressure_mmhg
    except:
        return 26.1, 1.1  # 返回观测到的固定值

def test_calculations():
    """测试计算函数"""
    print("=== 测试温度压力计算 ===")
    
    # 测试数据来自您的实际输出
    test_cases = [
        (14165.1640625, 18751.2734375),  # 第一组数据
        (14166.2109375, 18750.9453125),  # 第二组数据
        (14166.0703125, 18750.4765625),  # 第三组数据
        (14164.95703125, 18751.6875),    # 第四组数据
        (14165.6015625, 18750.8671875),  # 第五组数据
    ]
    
    print("基于观测数据的计算结果:")
    print("参考点: 温度腔14165nm -> 26.1°C, 压力腔18750nm -> 1.1mmHg")
    print("-" * 60)
    
    for i, (temp_cavity, press_cavity) in enumerate(test_cases, 1):
        temp, press = calculate_with_observed_data(temp_cavity, press_cavity)
        print(f"测试 {i}: 温度腔={temp_cavity:.1f}nm, 压力腔={press_cavity:.1f}nm")
        print(f"        计算结果: 温度={temp:.1f}°C, 压力={press:.2f}mmHg")
        print()
    
    print("=== 分析 ===")
    print("从数据可以看出:")
    print("1. 温度腔长变化范围: 14164.9 ~ 14166.2 nm (约1.3nm)")
    print("2. 压力腔长变化范围: 18750.5 ~ 18751.7 nm (约1.2nm)")
    print("3. 如果实际温度是26.1°C，压力是1.1mmHg，那么:")
    print("   - 温度系数应该很小 (因为腔长变化小)")
    print("   - 压力系数也应该很小")
    print()
    print("建议:")
    print("1. 需要获取真实的系数文件")
    print("2. 或者通过多点标定确定正确的系数")

if __name__ == "__main__":
    test_calculations()
