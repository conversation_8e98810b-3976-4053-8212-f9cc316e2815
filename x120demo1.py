
from ctypes import *

libx120 = cdll.LoadLibrary("./libx120-dev-sdk.so")

funtions = [ "退出", "设置传感器系数", "查看温补状态", "设置温补开关", "计算温度", "计算压力", "压力校零", "温度校N" ]
# 显示功能界面
def info_print():
    """显示功能界面"""
    print_hint('----------')
    print_hint('请选择功能')
    print_hint('----------')
    i = 1;
    while i <= 7:
        print(f'{i}、{funtions[i]}')
        i = i + 1
    print('0、退出')
    print_hint('----------')

def print_error(str):
    print(f'xxxxxxxxxx{str}xxxxxxxxxx')

def print_hint(str):
    print(f'----------{str}----------')

# 1、设置传感器系数


def call_set_sensor_coefficients(no):
    """设置传感器系数"""
    print_hint(funtions[no])
    data = input("输入传感器系数：")
    c_str = c_char_p(data.encode('utf-8'))
    libx120.set_sensor_coefficients.argtypes = [c_char_p]
    libx120.set_sensor_coefficients.restype = c_bool
    flag = libx120.set_sensor_coefficients(c_str)
    if flag:
        print_hint('设置系数成功')
    else:
        print_error('设置系数失败')

# 2、读取压力温度补偿开关的状态


def call_is_temperature_compensation_enabled(no):
    """读取压力温度补偿开关的状态"""
    print_hint(funtions[no])
    flag = libx120.is_temperature_compensation_enabled()
    if flag:
        print_hint("温补已开启")
    else:
        print_hint("温补已关闭")

# 3、设置压力温度补偿开关


def call_enable_temperature_compensation(no):
    """设置压力温度补偿开关"""
    print_hint(funtions[no])
    no = int(input("输入序号（0-关闭温补，1-开启温补）："))
    if no == 0:
        libx120.enable_temperature_compensation(False)
        print_hint('关闭温补完成')
    elif no == 1:
        libx120.enable_temperature_compensation(True)
        print_hint('开启温补完成')
    else:
        print_error("序号无效")

# 4、计算温度


def call_compute_temperature(no):
    """计算温度"""
    print_hint(funtions[no])
    cl1 = float(input("输入通道1腔长："))
    libx120.compute_temperature.argtypes = [c_float]
    libx120.compute_temperature.restype = c_float
    temp = libx120.compute_temperature(cl1);
    print_hint(f'计算温度：{temp}')

# 5、计算压力


def call_compute_pressure(no):
    """计算压力"""
    print_hint(funtions[no])
    cl1 = float(input("输入通道1腔长："))
    cl2 = float(input("输入通道2腔长："))
    libx120.compute_pressure.argtypes = [c_float, c_float]
    libx120.compute_pressure.restype = c_float
    pressure = libx120.compute_pressure(cl1, cl2);
    print_hint(f'计算压力：{pressure}')

# 6、压力校零


def call_calibrate_pressure(no):
    """压力校零"""
    print_hint(funtions[no])
    cl1 = float(input("输入通道1腔长："))
    cl2 = float(input("输入通道2腔长："))
    libx120.calibrate_pressure.argtypes = [c_float, c_float]
    libx120.calibrate_pressure.restype = c_bool
    flag = libx120.calibrate_pressure(cl1, cl2)
    if flag:
        print_hint('压力校零成功')
    else:
        print_error('压力校零失败')

# 7、温度校N


def call_calibrate_temperature(no):
    """温度校N"""
    print_hint(funtions[no])
    temp = float(input("输入校零温度："))
    cl1 = float(input("输入通道1腔长："))
    libx120.calibrate_temperature.argtypes = [c_float, c_float]
    libx120.calibrate_temperature.restype = c_bool
    flag = libx120.calibrate_temperature(temp, cl1)
    if flag:
        print_hint('温度校N成功')
    else:
        print_error('温度校N失败')

# 运行程序
while True:
    info_print()

    no = int(input("输入功能序号："))
    if no == 1:
        call_set_sensor_coefficients(1)
    elif no == 2:
        call_is_temperature_compensation_enabled(2)
    elif no == 3:
        call_enable_temperature_compensation(3)
    elif no == 4:
        call_compute_temperature(4)
    elif no == 5:
        call_compute_pressure(5)
    elif no == 6:
        call_calibrate_pressure(6)
    elif no == 7:
        call_calibrate_temperature(7)
    elif no == 0:
        print_hint("退出")
        quit(0)
    else:
        print_error("无效功能序号")


