#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JR-3220DN 串口通讯模块
基于PDF文档实现完整的串口通讯协议
"""

import serial
import serial.tools.list_ports
import time
import struct
import binascii
import zlib
from typing import Optional, List, Tuple, Dict, Any

class JR3220Serial:
    """JR-3220DN 串口通讯类"""
    
    # 串口配置参数（基于PDF文档）
    BAUDRATE = 57600  # 57.6kbps
    BYTESIZE = serial.EIGHTBITS  # 8位数据位
    PARITY = serial.PARITY_NONE  # 无奇偶校验
    STOPBITS = serial.STOPBITS_ONE  # 1位停止位
    TIMEOUT = 1.0  # 读取超时时间
    
    # 命令定义（基于PDF文档）
    CMD_ENABLE_CAVITY_DATA = bytes.fromhex('FA070001')  # 使能腔长数据发送
    CMD_DISABLE_CAVITY_DATA = bytes.fromhex('FA070000')  # 停止腔长数据发送
    CMD_VERSION_QUERY = bytes.fromhex('FB03')  # 版本查询
    CMD_PRESSURE_QUERY_1 = bytes.fromhex('FB57')  # 环境压力查询1
    CMD_PRESSURE_QUERY_2 = bytes.fromhex('FB56')  # 环境压力查询2
    CMD_CHIP_COEFF_QUERY = bytes.fromhex('F0EE')  # 芯片系数查询
    
    # 响应标识
    CAVITY_DATA_HEADER = bytes.fromhex('FEFF')  # 腔长数据帧头
    SENSOR_INSERT_SIGNAL = bytes.fromhex('EEEEEE')  # 传感器插入信号
    CHIP_COEFF_HEADER = bytes.fromhex('F1FF')  # 芯片系数帧头
    CHIP_COEFF_TAIL = bytes.fromhex('FFFF')  # 芯片系数帧尾
    CHIP_COEFF_SEPARATOR = bytes.fromhex('EEEE')  # 芯片系数分隔符
    
    def __init__(self, port: Optional[str] = None, debug: bool = False):
        """
        初始化串口通讯
        
        Args:
            port: 串口名称，如果为None则自动检测
            debug: 是否启用调试模式
        """
        self.port = port
        self.debug = debug
        self.serial_conn: Optional[serial.Serial] = None
        self.is_connected = False
        
    def list_available_ports(self) -> List[str]:
        """列出所有可用的串口"""
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append(port.device)
            if self.debug:
                print(f"发现串口: {port.device} - {port.description}")
        return ports
    
    def connect(self, port: Optional[str] = None) -> bool:
        """
        连接串口
        
        Args:
            port: 串口名称，如果为None则使用初始化时的端口
            
        Returns:
            bool: 连接是否成功
        """
        if port:
            self.port = port
            
        if not self.port:
            # 自动检测串口
            available_ports = self.list_available_ports()
            if not available_ports:
                if self.debug:
                    print("未找到可用的串口")
                return False
            self.port = available_ports[0]
            if self.debug:
                print(f"自动选择串口: {self.port}")
        
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.BAUDRATE,
                bytesize=self.BYTESIZE,
                parity=self.PARITY,
                stopbits=self.STOPBITS,
                timeout=self.TIMEOUT
            )
            
            # 清空缓冲区
            self.serial_conn.flushInput()
            self.serial_conn.flushOutput()
            
            self.is_connected = True
            if self.debug:
                print(f"成功连接到串口: {self.port}")
            return True
            
        except Exception as e:
            if self.debug:
                print(f"连接串口失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            self.is_connected = False
            if self.debug:
                print("串口连接已断开")
    
    def send_command(self, command: bytes, delay: float = 0.1) -> bool:
        """
        发送命令
        
        Args:
            command: 要发送的命令字节
            delay: 发送后的延迟时间（秒）
            
        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected or not self.serial_conn:
            if self.debug:
                print("串口未连接")
            return False
        
        try:
            self.serial_conn.write(command)
            if self.debug:
                print(f"发送命令: {command.hex().upper()}")
            
            # 添加延迟，等待设备响应
            time.sleep(delay)
            return True
            
        except Exception as e:
            if self.debug:
                print(f"发送命令失败: {e}")
            return False
    
    def read_response(self, expected_length: Optional[int] = None, timeout: float = 1.0) -> Optional[bytes]:
        """
        读取响应数据
        
        Args:
            expected_length: 期望的数据长度，如果为None则读取所有可用数据
            timeout: 读取超时时间
            
        Returns:
            bytes: 读取到的数据，如果失败返回None
        """
        if not self.is_connected or not self.serial_conn:
            if self.debug:
                print("串口未连接")
            return None
        
        try:
            # 设置临时超时时间
            original_timeout = self.serial_conn.timeout
            self.serial_conn.timeout = timeout
            
            if expected_length:
                data = self.serial_conn.read(expected_length)
            else:
                # 读取所有可用数据
                data = b''
                while self.serial_conn.in_waiting > 0:
                    chunk = self.serial_conn.read(self.serial_conn.in_waiting)
                    data += chunk
                    time.sleep(0.01)  # 短暂延迟，等待更多数据
            
            # 恢复原始超时时间
            self.serial_conn.timeout = original_timeout
            
            if data and self.debug:
                print(f"接收数据: {data.hex().upper()}")
            
            return data if data else None
            
        except Exception as e:
            if self.debug:
                print(f"读取响应失败: {e}")
            return None
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()

    # ==================== 数据解析功能 ====================

    def parse_cavity_data(self, data: bytes) -> Optional[Dict[str, float]]:
        """
        解析腔长数据（8字节格式）

        Args:
            data: 8字节的腔长数据

        Returns:
            dict: 包含温度和压力通道腔长的字典，失败返回None
        """
        if len(data) != 8:
            if self.debug:
                print(f"腔长数据长度错误: {len(data)} (期望8字节)")
            return None

        # 检查帧头
        if data[:2] != self.CAVITY_DATA_HEADER:
            if self.debug:
                print(f"腔长数据帧头错误: {data[:2].hex()} (期望FEFF)")
            return None

        try:
            # 解析温度通道腔长 (第3-5字节)
            temp_int = struct.unpack('>H', data[2:4])[0]  # 大端序，无符号短整型
            temp_frac = data[4] / 256.0
            temp_cavity = temp_int + temp_frac

            # 解析压力通道腔长 (第6-8字节)
            press_int = struct.unpack('>H', data[5:7])[0]  # 大端序，无符号短整型
            press_frac = data[7] / 256.0
            press_cavity = press_int + press_frac

            result = {
                'temperature_cavity': temp_cavity,
                'pressure_cavity': press_cavity,
                'unit': 'nm'
            }

            if self.debug:
                print(f"解析腔长数据: 温度通道={temp_cavity:.3f}nm, 压力通道={press_cavity:.3f}nm")

            return result

        except Exception as e:
            if self.debug:
                print(f"解析腔长数据失败: {e}")
            return None

    def parse_pressure_data(self, data1: bytes, data2: bytes) -> Optional[float]:
        """
        解析环境压力数据

        Args:
            data1: FB57命令的响应数据
            data2: FB56命令的响应数据

        Returns:
            float: 环境压力值(Pa)，失败返回None
        """
        try:
            # 检查响应格式
            if len(data1) < 4 or len(data2) < 4:
                if self.debug:
                    print("压力数据长度不足")
                return None

            if not (data1.startswith(bytes.fromhex('FC57')) and data2.startswith(bytes.fromhex('FC56'))):
                if self.debug:
                    print("压力数据格式错误")
                return None

            # 提取压力数据
            # data1: FC57####*, data2: FC56####
            # 需要组合成5字节的压力数据：data1的最后1字节 + data2的2字节
            if len(data1) >= 5 and len(data2) >= 6:
                # 完整格式：FC57####* 和 FC56####
                pressure_bytes = data1[4:5] + data2[4:6]  # 3字节
                pressure_bytes = b'\x00' + pressure_bytes  # 补齐为4字节
            else:
                # 简化格式：FC57#### 和 FC56####
                pressure_bytes = data1[2:4] + data2[2:4]  # 4字节

            pressure_value = struct.unpack('>I', pressure_bytes)[0]  # 大端序，无符号整型

            # 根据文档：除以4得到实际压力值
            pressure_pa = pressure_value / 4.0

            if self.debug:
                print(f"解析环境压力: {pressure_pa:.1f} Pa")

            return pressure_pa

        except Exception as e:
            if self.debug:
                print(f"解析压力数据失败: {e}")
            return None

    def parse_chip_coefficient(self, data: bytes) -> Optional[Dict[str, Any]]:
        """
        解析芯片系数数据

        Args:
            data: 芯片系数响应数据

        Returns:
            dict: 包含系数信息的字典，失败返回None
        """
        if len(data) < 6:
            if self.debug:
                print("芯片系数数据长度不足")
            return None

        # 检查是否为空响应（未检测到芯片）
        if data == self.CHIP_COEFF_TAIL or data.startswith(self.CHIP_COEFF_TAIL):
            if self.debug:
                print("检测到无芯片响应: FFFF")
            return {'status': 'no_chip', 'message': '未检测到系数芯片'}

        # 检查帧头和帧尾
        if not data.startswith(self.CHIP_COEFF_HEADER):
            if self.debug:
                print("芯片系数帧头错误")
            return None

        if not data.endswith(self.CHIP_COEFF_TAIL):
            if self.debug:
                print("芯片系数帧尾错误")
            return None

        try:
            # 查找分隔符位置
            separator_pos = data.find(self.CHIP_COEFF_SEPARATOR)
            if separator_pos == -1:
                if self.debug:
                    print("未找到芯片系数分隔符")
                return None

            # 提取系数主体
            coeff_body = data[2:separator_pos]  # 跳过帧头F1FF

            # 提取CRC校验码
            crc_start = separator_pos + 2  # 跳过分隔符EEEE
            crc_bytes = data[crc_start:crc_start+4]

            if len(crc_bytes) != 4:
                if self.debug:
                    print("CRC校验码长度错误")
                return None

            # 将系数主体转换为ASCII文本
            try:
                coeff_text = coeff_body.decode('ascii')
            except UnicodeDecodeError:
                if self.debug:
                    print("系数主体不是有效的ASCII码")
                return None

            # 验证CRC校验码
            calculated_crc = zlib.crc32(coeff_body) & 0xffffffff
            received_crc = struct.unpack('>I', crc_bytes)[0]

            crc_valid = calculated_crc == received_crc

            if self.debug:
                print(f"芯片系数文本: {coeff_text}")
                print(f"CRC校验: {'通过' if crc_valid else '失败'}")

            return {
                'status': 'success',
                'coefficient_text': coeff_text,
                'crc_valid': crc_valid,
                'calculated_crc': calculated_crc,
                'received_crc': received_crc
            }

        except Exception as e:
            if self.debug:
                print(f"解析芯片系数失败: {e}")
            return None

    # ==================== 高级命令功能 ====================

    def enable_cavity_data_transmission(self) -> bool:
        """启用腔长数据传输"""
        return self.send_command(self.CMD_ENABLE_CAVITY_DATA)

    def disable_cavity_data_transmission(self) -> bool:
        """停止腔长数据传输"""
        return self.send_command(self.CMD_DISABLE_CAVITY_DATA)

    def query_version(self) -> Optional[str]:
        """
        查询模块版本信息

        Returns:
            str: 版本信息，失败返回None
        """
        if not self.send_command(self.CMD_VERSION_QUERY):
            return None

        response = self.read_response(timeout=2.0)
        if not response or len(response) < 4:
            return None

        # 检查响应格式 FC03**##
        if not response.startswith(bytes.fromhex('FC03')):
            if self.debug:
                print("版本查询响应格式错误")
            return None

        try:
            version_bytes = response[2:4]
            version = f"{version_bytes[0]:02d}.{version_bytes[1]:02d}"
            if self.debug:
                print(f"模块版本: {version}")
            return version
        except Exception as e:
            if self.debug:
                print(f"解析版本信息失败: {e}")
            return None

    def query_environmental_pressure(self) -> Optional[float]:
        """
        查询环境压力

        Returns:
            float: 环境压力值(Pa)，失败返回None
        """
        # 发送第一个压力查询命令
        if not self.send_command(self.CMD_PRESSURE_QUERY_1):
            return None

        response1 = self.read_response(timeout=2.0)
        if not response1:
            return None

        # 发送第二个压力查询命令
        if not self.send_command(self.CMD_PRESSURE_QUERY_2):
            return None

        response2 = self.read_response(timeout=2.0)
        if not response2:
            return None

        return self.parse_pressure_data(response1, response2)

    def query_chip_coefficient(self) -> Optional[Dict[str, Any]]:
        """
        查询芯片系数

        Returns:
            dict: 芯片系数信息，失败返回None
        """
        if not self.send_command(self.CMD_CHIP_COEFF_QUERY):
            return None

        # 芯片系数数据可能比较长，需要更长的超时时间
        response = self.read_response(timeout=5.0)
        if not response:
            return None

        return self.parse_chip_coefficient(response)

    def read_cavity_data_stream(self, duration: float = 10.0) -> List[Dict[str, float]]:
        """
        读取腔长数据流

        Args:
            duration: 读取持续时间（秒）

        Returns:
            list: 腔长数据列表
        """
        cavity_data_list = []
        start_time = time.time()

        # 启用腔长数据传输
        if not self.enable_cavity_data_transmission():
            return cavity_data_list

        try:
            while time.time() - start_time < duration:
                data = self.read_response(expected_length=8, timeout=0.5)
                if data and len(data) == 8:
                    parsed_data = self.parse_cavity_data(data)
                    if parsed_data:
                        parsed_data['timestamp'] = time.time()
                        cavity_data_list.append(parsed_data)

                # 检查是否有传感器插入信号
                if data and data == self.SENSOR_INSERT_SIGNAL:
                    if self.debug:
                        print("检测到传感器插入信号")

                time.sleep(0.01)  # 短暂延迟

        finally:
            # 停止腔长数据传输
            self.disable_cavity_data_transmission()

        return cavity_data_list

    def get_device_info(self) -> Dict[str, Any]:
        """
        获取设备完整信息

        Returns:
            dict: 设备信息字典
        """
        info = {
            'connected': self.is_connected,
            'port': self.port,
            'version': None,
            'environmental_pressure': None,
            'chip_coefficient': None
        }

        if not self.is_connected:
            return info

        # 查询版本信息
        info['version'] = self.query_version()

        # 查询环境压力
        info['environmental_pressure'] = self.query_environmental_pressure()

        # 查询芯片系数
        info['chip_coefficient'] = self.query_chip_coefficient()

        return info
