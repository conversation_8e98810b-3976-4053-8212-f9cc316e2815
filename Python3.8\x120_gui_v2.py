#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
X120传感器控制系统 - GUI版本V2
完全按照提供的界面图片设计
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib
import numpy as np

# 配置matplotlib中文字体
try:
    matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
    matplotlib.rcParams['axes.unicode_minus'] = False
    print("中文字体配置成功")
except Exception as e:
    print(f"字体配置失败: {e}")
    # 使用默认字体
    pass
import threading
import time
import queue
from datetime import datetime
# import random  # 不再需要模拟数据
import json
import os
from jr3220_serial import JR3220Serial

class CoefficientManager:
    """温压系数管理器"""

    def __init__(self):
        self.coefficients = {
            'temperature': {
                'A0': 0.0, 'A1': 0.0, 'A2': 0.0, 'A3': 0.0,
                'B0': 0.0, 'B1': 0.0, 'B2': 0.0, 'B3': 0.0
            },
            'pressure': {
                'C0': 0.0, 'C1': 0.0, 'C2': 0.0, 'C3': 0.0,
                'D0': 0.0, 'D1': 0.0, 'D2': 0.0, 'D3': 0.0
            },
            'reference_temperature': 25.0,
            'reference_pressure': 101325.0
        }
        self.coefficients_loaded = False

    def load_from_file(self, filepath):
        """从文件加载系数"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.coefficients.update(data)
                self.coefficients_loaded = True
                return True, "系数加载成功"
        except Exception as e:
            return False, f"加载系数失败: {e}"

    def load_from_chip_data(self, chip_data):
        """从芯片数据解析系数 - 基于JR-3220DN文档格式"""
        try:
            print(f"解析芯片数据: {chip_data[:100]}...")

            # 解析芯片数据中的系数信息
            lines = chip_data.strip().split('\n')
            sensor_id = None
            coefficients_values = []

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 第一行通常是传感器编号 (如 FS5025)
                if not sensor_id and not '=' in line and not ':' in line and not 'E' in line:
                    sensor_id = line
                    print(f"检测到传感器编号: {sensor_id}")
                    continue

                # 跳过标题行（如"温度系数:"、"压力系数:"）
                if ':' in line and '=' not in line:
                    continue

                # 解析系数行 (支持科学计数法，如 1.300322E+0)
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value_str = value.strip()

                    try:
                        value = float(value_str)
                        # 根据键名分配到对应的系数组
                        if key in ['A0', 'A1', 'A2', 'A3']:
                            self.coefficients['temperature'][key] = value
                        elif key in ['B0', 'B1', 'B2', 'B3']:
                            self.coefficients['pressure'][key] = value
                    except ValueError:
                        continue
                else:
                    # 尝试解析纯数值行 (如文档中的格式)
                    try:
                        value = float(line)
                        coefficients_values.append(value)
                    except ValueError:
                        continue

            # 如果找到了纯数值格式的系数 (如文档示例)
            if len(coefficients_values) >= 9:
                print(f"检测到{len(coefficients_values)}个系数值")
                # 根据文档，前4个是温度系数，后4个是压力系数
                self.coefficients['temperature']['A0'] = coefficients_values[0]
                self.coefficients['temperature']['A1'] = coefficients_values[1]
                self.coefficients['temperature']['A2'] = coefficients_values[2]
                self.coefficients['temperature']['A3'] = coefficients_values[3]

                self.coefficients['pressure']['B0'] = coefficients_values[4]
                self.coefficients['pressure']['B1'] = coefficients_values[5]
                self.coefficients['pressure']['B2'] = coefficients_values[6]
                self.coefficients['pressure']['B3'] = coefficients_values[7]

                print(f"温度系数: A0={coefficients_values[0]}, A1={coefficients_values[1]}")
                print(f"压力系数: B0={coefficients_values[4]}, B1={coefficients_values[5]}")

            self.coefficients_loaded = True
            return True, f"芯片系数解析成功 (传感器: {sensor_id or '未知'})"
        except Exception as e:
            print(f"解析芯片系数异常: {e}")
            return False, f"解析芯片系数失败: {e}"

    def compute_temperature(self, cavity_length):
        """根据腔长计算温度"""
        if not self.coefficients_loaded:
            return None

        try:
            temp_coeff = self.coefficients['temperature']
            cl = cavity_length

            # 使用多项式计算温度 T = A0 + A1*CL + A2*CL^2 + A3*CL^3 + B0*exp(B1*CL)
            temperature = (temp_coeff['A0'] +
                          temp_coeff['A1'] * cl +
                          temp_coeff['A2'] * cl**2 +
                          temp_coeff['A3'] * cl**3 +
                          temp_coeff['B0'] * np.exp(temp_coeff['B1'] * cl))

            return temperature
        except Exception as e:
            print(f"温度计算错误: {e}")
            return None

    def compute_pressure(self, temp_cavity, press_cavity, temperature=None):
        """根据腔长计算压力"""
        if not self.coefficients_loaded:
            return None

        try:
            press_coeff = self.coefficients['pressure']
            cl1, cl2 = temp_cavity, press_cavity

            # 如果没有提供温度，先计算温度
            if temperature is None:
                temperature = self.compute_temperature(cl1)
                if temperature is None:
                    return None

            # 使用多项式计算压力 P = C0 + C1*CL2 + C2*CL2^2 + C3*CL2^3 + D0*T + D1*T^2
            pressure = (press_coeff['C0'] +
                       press_coeff['C1'] * cl2 +
                       press_coeff['C2'] * cl2**2 +
                       press_coeff['C3'] * cl2**3 +
                       press_coeff['D0'] * temperature +
                       press_coeff['D1'] * temperature**2)

            return pressure
        except Exception as e:
            print(f"压力计算错误: {e}")
            return None

    def has_valid_coefficients(self):
        """检查是否有有效的系数"""
        return self.coefficients_loaded

    def calculate_with_observed_data(self, temp_cavity, press_cavity):
        """基于观测数据的临时计算方法"""
        try:
            # 基于实际观测: 温度腔14165nm->26.1°C, 压力腔18750nm->1.1mmHg
            temp_ref_cavity = 14165.0
            temp_ref_value = 26.1
            temp_coefficient = 0.1  # 需要根据更多数据校准

            press_ref_cavity = 18750.0
            press_ref_value = 1.1
            press_coefficient = 0.01  # 需要根据更多数据校准

            temperature = temp_coefficient * (temp_cavity - temp_ref_cavity) + temp_ref_value
            pressure_mmhg = press_coefficient * (press_cavity - press_ref_cavity) + press_ref_value

            return temperature, pressure_mmhg
        except:
            return 26.1, 1.1  # 返回观测到的固定值

class X120GUIV2:
    def __init__(self, root):
        self.root = root
        self.root.title("白光干涉高温高压软件TP v2.6.8")
        self.root.geometry("1200x800")
        self.root.minsize(1200, 800)  # 设置最小窗口大小
        self.root.configure(bg='#f0f0f0')

        # 确保窗口显示在前台
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))

        print("GUI窗口已创建，大小: 1200x800")
        
        # 初始化变量
        self.serial_comm = None
        self.current_mode = "simulation"
        self.is_connected = False
        self.real_time_running = False
        self.data_queue = queue.Queue()
        self.use_real_data = False

        # 系数管理器
        self.coefficient_manager = CoefficientManager()

        # 数据存储
        self.temp_data = []
        self.press_data = []
        self.time_data = []

        # 界面变量
        self.setup_variables()

        # 创建界面
        self.create_widgets()
        self.setup_mock_library()
        self.start_data_thread()
        self.process_queue()

        # 尝试加载默认系数文件
        self.load_default_coefficients()

    def setup_variables(self):
        """设置界面变量"""
        # 串口配置
        self.serial_var = tk.StringVar(value="COM3")

        # 输出物理量配置
        self.output_var = tk.StringVar(value="温度&压力")

        # 温度输出单位
        self.temp_unit_var = tk.StringVar(value="温度(℃)")

        # 压力输出单位
        self.press_unit_var = tk.StringVar(value="压力(mmHg)")

        # 芯片连接器
        self.chip_var = tk.StringVar(value="无芯片连接器")

        # 数据保存方式
        self.data_save_var = tk.StringVar(value="不保存数据")

        # 温补开关
        self.temp_compensation_var = tk.BooleanVar(value=False)

        # 底部控制变量
        self.pressure_influence_var = tk.StringVar(value="不去除")
        self.temp_range_var = tk.StringVar(value="-10-50")
        self.press_range_var = tk.StringVar(value="-30-70")

    def load_default_coefficients(self):
        """加载默认系数文件"""
        default_files = ["coefficients.json", "sensor_coeff.json", "default_coeff.json"]
        for filename in default_files:
            if os.path.exists(filename):
                success, message = self.coefficient_manager.load_from_file(filename)
                if success:
                    print(f"已加载默认系数文件: {filename}")
                    self.update_coefficient_display(f"已加载: {filename}")
                    break
        else:
            print("未找到默认系数文件")

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建左右分割的主要区域
        content_frame = tk.Frame(main_frame, bg='#f0f0f0')
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧内容区域
        left_frame = tk.Frame(content_frame, bg='#f0f0f0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 右侧参数配置区域
        self.create_right_panel(content_frame)

        # 在左侧区域创建其他组件
        # 顶部数据显示区域
        self.create_top_display(left_frame)

        # 中间图表区域
        self.create_chart_area(left_frame)

        # 底部控制区域
        self.create_bottom_controls(left_frame)
    
    def create_top_display(self, parent):
        """创建顶部数据显示区域"""
        top_frame = tk.Frame(parent, bg='#f0f0f0')
        top_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 传感器编号
        sensor_frame = tk.Frame(top_frame, bg='#f0f0f0')
        sensor_frame.pack(side=tk.LEFT, padx=(0, 50))

        tk.Label(sensor_frame, text="传感器编号:", font=('Arial', 12), bg='#f0f0f0').pack()
        # 添加边框的数值显示
        temp_value_frame = tk.Frame(sensor_frame, bg='white', relief=tk.SUNKEN, bd=1)
        temp_value_frame.pack(pady=2)
        self.temp_value = tk.Label(temp_value_frame, text="0", font=('Arial', 24, 'bold'),
                                  fg='blue', bg='white', width=8)
        self.temp_value.pack(padx=5, pady=2)
        tk.Label(sensor_frame, text="℃(温度)", font=('Arial', 10), bg='#f0f0f0').pack()

        # 仪表编号
        instrument_frame = tk.Frame(top_frame, bg='#f0f0f0')
        instrument_frame.pack(side=tk.LEFT, padx=(0, 50))

        tk.Label(instrument_frame, text="仪表编号:", font=('Arial', 12), bg='#f0f0f0').pack()
        # 添加边框的数值显示
        press_value_frame = tk.Frame(instrument_frame, bg='white', relief=tk.SUNKEN, bd=1)
        press_value_frame.pack(pady=2)
        self.press_value = tk.Label(press_value_frame, text="0", font=('Arial', 24, 'bold'),
                                   fg='blue', bg='white', width=8)
        self.press_value.pack(padx=5, pady=2)
        tk.Label(instrument_frame, text="mmHg(压力)", font=('Arial', 10), bg='#f0f0f0').pack()
        
        # 仪表固件版本号
        version_frame = tk.Frame(top_frame, bg='#f0f0f0')
        version_frame.pack(side=tk.LEFT)
        
        tk.Label(version_frame, text="仪表固件版本号:", font=('Arial', 12), bg='#f0f0f0').pack()
        self.version_label = tk.Label(version_frame, text="v2.6.8", font=('Arial', 12), bg='#f0f0f0')
        self.version_label.pack()

        # 添加传感器详细信息显示区域
        info_detail_frame = tk.Frame(parent, bg='#f0f0f0', relief=tk.SUNKEN, bd=1)
        info_detail_frame.pack(fill='x', pady=(5, 10))

        # 传感器编号详细信息
        sensor_detail_frame = tk.Frame(info_detail_frame, bg='#f0f0f0')
        sensor_detail_frame.pack(side=tk.LEFT, fill='x', expand=True, padx=5, pady=5)

        tk.Label(sensor_detail_frame, text="传感器编号:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w')
        self.sensor_id_detail = tk.Label(sensor_detail_frame, text="未连接",
                                        font=('Arial', 10), bg='#f0f0f0', fg='gray')
        self.sensor_id_detail.pack(anchor='w')

        # 仪表编号详细信息
        meter_detail_frame = tk.Frame(info_detail_frame, bg='#f0f0f0')
        meter_detail_frame.pack(side=tk.LEFT, fill='x', expand=True, padx=5, pady=5)

        tk.Label(meter_detail_frame, text="仪表编号:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w')
        self.meter_id_detail = tk.Label(meter_detail_frame, text="未连接",
                                       font=('Arial', 10), bg='#f0f0f0', fg='gray')
        self.meter_id_detail.pack(anchor='w')

        # 仪表固件版本详细信息
        version_detail_frame = tk.Frame(info_detail_frame, bg='#f0f0f0')
        version_detail_frame.pack(side=tk.RIGHT, fill='x', expand=True, padx=5, pady=5)

        tk.Label(version_detail_frame, text="仪表固件版本号:", font=('Arial', 10), bg='#f0f0f0').pack(anchor='w')
        self.version_detail = tk.Label(version_detail_frame, text="F0120.1000.0107",
                                      font=('Arial', 10), bg='#f0f0f0')
        self.version_detail.pack(anchor='w')
    
    def create_chart_area(self, parent):
        """创建图表区域"""
        chart_frame = tk.Frame(parent, bg='white', relief=tk.SUNKEN, bd=1)
        chart_frame.pack(fill=tk.BOTH, expand=False, pady=(0, 10))
        chart_frame.config(height=400)  # 固定图表区域高度
        
        # 创建matplotlib图表
        self.fig = Figure(figsize=(10, 4.5), dpi=100, facecolor='white')
        self.ax = self.fig.add_subplot(111)
        
        # 设置图表样式
        self.ax.set_xlim(0, 30)
        self.ax.set_ylim(-10, 50)
        self.ax.set_xlabel('时间(30秒)', fontsize=10)
        self.ax.set_ylabel('温度(℃)', color='blue', fontsize=10)
        self.ax.tick_params(axis='y', labelcolor='blue')
        self.ax.grid(True, alpha=0.3)
        
        # 创建右侧Y轴（压力）
        self.ax2 = self.ax.twinx()
        self.ax2.set_ylabel('压力(mmHg)', color='green', fontsize=10)
        self.ax2.set_ylim(-30, 70)
        self.ax2.tick_params(axis='y', labelcolor='green')
        
        # 初始化数据线
        self.temp_line, = self.ax.plot([], [], 'b-', linewidth=2, label='温度')
        self.press_line, = self.ax2.plot([], [], 'g-', linewidth=2, label='压力')

        # 添加参考线（红色虚线）- 只保留水平线
        self.ax.axhline(y=20, color='red', linestyle='--', alpha=0.7, linewidth=1)
        self.ax2.axhline(y=0, color='blue', linestyle='-', alpha=0.5, linewidth=1)

        # 初始化鼠标十字线（开始时不可见）
        self.crosshair_v = self.ax.axvline(x=0, color='red', linestyle='-', alpha=0.8, linewidth=1)
        self.crosshair_h = self.ax.axhline(y=0, color='red', linestyle='-', alpha=0.8, linewidth=1)
        self.crosshair_v.set_visible(False)
        self.crosshair_h.set_visible(False)

        # 添加状态文本
        self.temp_status = self.ax.text(0.02, 0.95, '温度：距长：0.00，采样频率：0Hz',
                    transform=self.ax.transAxes, fontsize=9, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.9, edgecolor='gray'))
        self.press_status = self.ax.text(0.52, 0.95, '压力：距长：0.00，采样频率：0Hz',
                    transform=self.ax.transAxes, fontsize=9, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.9, edgecolor='gray'))
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 工具栏已移除，使用鼠标交互代替

        # 添加鼠标交互事件
        self.setup_mouse_interaction()

    def setup_mouse_interaction(self):
        """设置鼠标交互功能"""
        # 鼠标拖拽相关变量
        self.drag_start = None
        self.is_dragging = False

        # 绑定鼠标事件
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.canvas.mpl_connect('scroll_event', self.on_mouse_scroll)

    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if event.button == 1 and event.inaxes:  # 左键按下且在坐标轴内
            self.drag_start = (event.xdata, event.ydata)
            self.is_dragging = True
            # 改变鼠标光标为手型
            self.canvas.get_tk_widget().config(cursor="fleur")

    def on_mouse_release(self, event):
        """鼠标释放事件"""
        if event.button == 1:  # 左键释放
            self.is_dragging = False
            self.drag_start = None
            # 恢复默认光标
            self.canvas.get_tk_widget().config(cursor="")

    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if self.is_dragging and self.drag_start and event.inaxes and event.xdata and event.ydata:
            # 计算拖拽距离
            dx = self.drag_start[0] - event.xdata
            dy = self.drag_start[1] - event.ydata

            # 获取当前坐标轴范围
            xlim = self.ax.get_xlim()
            ylim = self.ax.get_ylim()
            ylim2 = self.ax2.get_ylim()

            # 平移坐标轴
            self.ax.set_xlim(xlim[0] + dx, xlim[1] + dx)
            self.ax.set_ylim(ylim[0] + dy, ylim[1] + dy)
            self.ax2.set_ylim(ylim2[0] + dy, ylim2[1] + dy)

            # 重新绘制
            self.canvas.draw_idle()
        elif event.inaxes == self.ax and event.xdata is not None and event.ydata is not None and not self.is_dragging:
            # 只在主坐标轴内更新十字线位置
            self.update_crosshair(event.xdata, event.ydata)
        else:
            # 鼠标不在坐标轴内时隐藏十字线
            self.hide_crosshair()

    def on_mouse_enter(self, event):
        """鼠标进入坐标轴区域"""
        if event.inaxes == self.ax:
            # 只在主坐标轴内显示十字线
            self.show_crosshair()

    def on_mouse_leave(self, event):
        """鼠标离开坐标轴区域"""
        # 隐藏十字线
        self.hide_crosshair()

    def show_crosshair(self):
        """显示十字线"""
        self.crosshair_v.set_visible(True)
        self.crosshair_h.set_visible(True)
        self.canvas.draw_idle()

    def hide_crosshair(self):
        """隐藏十字线"""
        self.crosshair_v.set_visible(False)
        self.crosshair_h.set_visible(False)
        self.canvas.draw_idle()

    def update_crosshair(self, x, y):
        """更新十字线位置"""
        try:
            # 确保十字线可见
            self.crosshair_v.set_visible(True)
            self.crosshair_h.set_visible(True)

            # 更新垂直线位置（X坐标）
            self.crosshair_v.set_xdata([x, x])

            # 更新水平线位置（Y坐标）- 使用鼠标实际的Y坐标
            self.crosshair_h.set_ydata([y, y])

            # 重新绘制
            self.canvas.draw_idle()
        except Exception as e:
            print(f"更新十字线时出错: {e}")

    def convert_pressure_to_temp_axis(self, pressure_y):
        """将压力轴的Y坐标转换为温度轴的Y坐标"""
        # 获取两个轴的范围
        temp_ylim = self.ax.get_ylim()
        press_ylim = self.ax2.get_ylim()

        # 计算压力值在压力轴中的相对位置（0-1）
        press_ratio = (pressure_y - press_ylim[0]) / (press_ylim[1] - press_ylim[0])

        # 将相对位置映射到温度轴
        temp_y = temp_ylim[0] + press_ratio * (temp_ylim[1] - temp_ylim[0])

        return temp_y

    def on_mouse_scroll(self, event):
        """鼠标滚轮事件"""
        if event.inaxes:
            # 缩放因子
            scale_factor = 1.1 if event.step < 0 else 1/1.1

            # 获取鼠标位置
            xdata, ydata = event.xdata, event.ydata

            # 获取当前坐标轴范围
            xlim = self.ax.get_xlim()
            ylim = self.ax.get_ylim()
            ylim2 = self.ax2.get_ylim()

            # 计算新的范围（以鼠标位置为中心缩放）
            new_width = (xlim[1] - xlim[0]) * scale_factor
            new_height = (ylim[1] - ylim[0]) * scale_factor
            new_height2 = (ylim2[1] - ylim2[0]) * scale_factor

            # 计算新的边界
            relx = (xlim[1] - xdata) / (xlim[1] - xlim[0])
            rely = (ylim[1] - ydata) / (ylim[1] - ylim[0])
            rely2 = (ylim2[1] - ydata) / (ylim2[1] - ylim2[0])

            new_xlim = [xdata - new_width * (1-relx), xdata + new_width * relx]
            new_ylim = [ydata - new_height * (1-rely), ydata + new_height * rely]
            new_ylim2 = [ydata - new_height2 * (1-rely2), ydata + new_height2 * rely2]

            # 应用新的范围
            self.ax.set_xlim(new_xlim)
            self.ax.set_ylim(new_ylim)
            self.ax2.set_ylim(new_ylim2)

            # 重新绘制
            self.canvas.draw_idle()

    def create_bottom_controls(self, parent):
        """创建底部控制区域"""
        bottom_frame = tk.Frame(parent, bg='#f0f0f0')
        bottom_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 左侧控制组
        left_controls = tk.Frame(bottom_frame, bg='#f0f0f0')
        left_controls.pack(side=tk.LEFT)
        
        # 去除气压影响
        pressure_frame = tk.Frame(left_controls, bg='#f0f0f0')
        pressure_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(pressure_frame, text="去除气压影响", font=('Arial', 10), bg='#f0f0f0').pack()
        self.pressure_var = tk.StringVar(value="不去除")
        pressure_combo = ttk.Combobox(pressure_frame, textvariable=self.pressure_var,
                                     values=["不去除", "去除", "自动补偿"], width=8, state="readonly")
        pressure_combo.pack()
        pressure_combo.bind('<<ComboboxSelected>>', self.on_pressure_change)

        # 温度显示范围
        temp_range_frame = tk.Frame(left_controls, bg='#f0f0f0')
        temp_range_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(temp_range_frame, text="温度显示范围", font=('Arial', 10), bg='#f0f0f0').pack()
        self.temp_range_var = tk.StringVar(value="-10-50")
        temp_range_combo = ttk.Combobox(temp_range_frame, textvariable=self.temp_range_var,
                                       values=["-10-50", "0-100", "-20-80", "0-50", "-50-100"], width=8, state="readonly")
        temp_range_combo.pack()
        temp_range_combo.bind('<<ComboboxSelected>>', self.on_temp_range_change)

        # 压力显示范围
        press_range_frame = tk.Frame(left_controls, bg='#f0f0f0')
        press_range_frame.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(press_range_frame, text="压力显示范围", font=('Arial', 10), bg='#f0f0f0').pack()
        self.press_range_var = tk.StringVar(value="-30-70")
        press_range_combo = ttk.Combobox(press_range_frame, textvariable=self.press_range_var,
                                        values=["-30-70", "0-100", "-50-100", "0-200", "-100-200"], width=8, state="readonly")
        press_range_combo.pack()
        press_range_combo.bind('<<ComboboxSelected>>', self.on_press_range_change)
        
        # 中间按钮组
        center_controls = tk.Frame(bottom_frame, bg='#f0f0f0')
        center_controls.pack(side=tk.LEFT, padx=50)
        
        # 开始接收数据按钮
        self.start_btn = tk.Button(center_controls, text="开始接收数据", font=('Arial', 12, 'bold'),
                                  bg='#4472C4', fg='white', width=12, height=2,
                                  command=self.toggle_data_collection)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        # 右侧按钮组
        right_controls = tk.Frame(bottom_frame, bg='#f0f0f0')
        right_controls.pack(side=tk.RIGHT)
        
        # 温度校零按钮
        temp_cal_btn = tk.Button(right_controls, text="温度校零", font=('Arial', 10),
                                bg='#D9D9D9', width=8, height=2, command=self.temp_calibration)
        temp_cal_btn.pack(side=tk.LEFT, padx=2)

        # 压力校零按钮
        press_cal_btn = tk.Button(right_controls, text="压力校零", font=('Arial', 10),
                                 bg='#D9D9D9', width=8, height=2, command=self.press_calibration)
        press_cal_btn.pack(side=tk.LEFT, padx=2)
    
    def create_right_panel(self, parent):
        """创建右侧参数配置面板"""
        # 创建右侧面板框架
        right_panel = tk.Frame(parent, bg='#e8e8e8', width=250, relief=tk.RAISED, bd=1)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_panel.pack_propagate(False)

        # 创建滚动区域
        canvas = tk.Canvas(right_panel, bg='#e8e8e8', highlightthickness=0)
        scrollbar = tk.Scrollbar(right_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#e8e8e8')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 参数配置标题
        tk.Label(scrollable_frame, text="参数配置", font=('Arial', 14, 'bold'),
                bg='#e8e8e8').pack(pady=(10, 10))
        
        # 串口配置
        self.create_serial_config(scrollable_frame)

        # 输出物理量配置
        self.create_output_config(scrollable_frame)

        # 温度输出单位配置
        self.create_temp_unit_config(scrollable_frame)

        # 压力输出单位配置
        self.create_press_unit_config(scrollable_frame)

        # 芯片连接器配置
        self.create_chip_config(scrollable_frame)

        # 数据保存配置
        self.create_data_save_config(scrollable_frame)

        # 温补开关
        self.create_temp_compensation(scrollable_frame)

        # 温度系数配置
        self.create_temp_coefficient(scrollable_frame)

        # 已选择传感器系数
        self.create_sensor_coefficient(scrollable_frame)
    
    def create_serial_config(self, parent):
        """创建串口配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="串口", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)

        serial_frame = tk.Frame(frame, bg='#e8e8e8')
        serial_frame.pack(fill=tk.X)

        # 串口选择
        self.serial_var = tk.StringVar(value="COM3")
        serial_combo = ttk.Combobox(serial_frame, textvariable=self.serial_var,
                                   values=["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8"], width=15)
        serial_combo.pack(side=tk.LEFT)
        serial_combo.bind('<<ComboboxSelected>>', self.on_serial_change)

        # 程序启动时自动尝试连接可用串口
        self.root.after(1000, self.auto_connect_serial)

        # 连接状态指示灯
        self.status_indicator = tk.Label(serial_frame, text="●", font=('Arial', 16),
                                        fg='red', bg='#e8e8e8')
        self.status_indicator.pack(side=tk.RIGHT)

        # 添加测试连接按钮
        test_btn = tk.Button(serial_frame, text="测试连接", font=('Arial', 8),
                           bg='#4472C4', fg='white', width=8,
                           command=self.test_all_ports)
        test_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 添加测试参数按钮
        param_btn = tk.Button(serial_frame, text="测试参数", font=('Arial', 8),
                            bg='#FF6B35', fg='white', width=8,
                            command=self.test_serial_parameters)
        param_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 添加模拟数据按钮
        sim_btn = tk.Button(serial_frame, text="模拟数据", font=('Arial', 8),
                          bg='#28A745', fg='white', width=8,
                          command=self.enable_simulation_mode)
        sim_btn.pack(side=tk.RIGHT, padx=(5, 0))

        # 添加原始数据监听按钮
        raw_btn = tk.Button(serial_frame, text="监听数据", font=('Arial', 8),
                          bg='#6F42C1', fg='white', width=8,
                          command=self.start_raw_data_listener)
        raw_btn.pack(side=tk.RIGHT, padx=(5, 0))
    
    def create_output_config(self, parent):
        """创建输出物理量配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="输出物理量", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.output_var = tk.StringVar(value="温度&压力")
        output_combo = ttk.Combobox(frame, textvariable=self.output_var,
                                   values=["温度&压力", "温度", "压力"], width=15, state="readonly")
        output_combo.pack(fill=tk.X)
        output_combo.bind('<<ComboboxSelected>>', self.on_output_change)
    
    def create_temp_unit_config(self, parent):
        """创建温度输出单位配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="温度输出单位", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.temp_unit_var = tk.StringVar(value="温度(℃)")
        temp_unit_combo = ttk.Combobox(frame, textvariable=self.temp_unit_var,
                                      values=["温度(℃)", "华氏度(℉)", "开尔文(K)"], width=15, state="readonly")
        temp_unit_combo.pack(fill=tk.X)
        temp_unit_combo.bind('<<ComboboxSelected>>', self.on_temp_unit_change)

    def create_press_unit_config(self, parent):
        """创建压力输出单位配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="压力输出单位", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.press_unit_var = tk.StringVar(value="压力(mmHg)")
        press_unit_combo = ttk.Combobox(frame, textvariable=self.press_unit_var,
                                       values=["压力(mmHg)", "PSI", "压力(kPa)", "压力(bar)", "压力(Pa)"],
                                       width=15, state="readonly")
        press_unit_combo.pack(fill=tk.X)
        press_unit_combo.bind('<<ComboboxSelected>>', self.on_press_unit_change)

    def create_chip_config(self, parent):
        """创建芯片连接器配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="芯片连接器", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.chip_var = tk.StringVar(value="无芯片连接器")
        chip_combo = ttk.Combobox(frame, textvariable=self.chip_var,
                                 values=["无芯片连接器", "存在芯片连接器"], width=15, state="readonly")
        chip_combo.pack(fill=tk.X)
        chip_combo.bind('<<ComboboxSelected>>', self.on_chip_change)

    def create_data_save_config(self, parent):
        """创建数据保存配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="数据保存方式", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.data_save_var = tk.StringVar(value="不保存数据")
        data_save_combo = ttk.Combobox(frame, textvariable=self.data_save_var,
                                      values=["不保存数据", "保存到CSV", "保存到Excel", "保存到TXT"], width=15, state="readonly")
        data_save_combo.pack(fill=tk.X)
        data_save_combo.bind('<<ComboboxSelected>>', self.on_data_save_change)
    
    def create_temp_compensation(self, parent):
        """创建温补开关"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=10, padx=10)

        # 温补开关
        switch_frame = tk.Frame(frame, bg='#e8e8e8')
        switch_frame.pack(fill=tk.X)

        self.temp_comp_var = tk.BooleanVar(value=True)
        switch_bg = tk.Frame(switch_frame, bg='#ff69b4', width=60, height=25)
        switch_bg.pack(side=tk.LEFT)
        switch_bg.pack_propagate(False)

        tk.Label(switch_bg, text="●", font=('Arial', 16), fg='white', bg='#ff69b4').pack(side=tk.RIGHT)

        tk.Label(switch_frame, text="是否开启压力温补", font=('Arial', 10),
                bg='#e8e8e8').pack(side=tk.LEFT, padx=(10, 0))
    
    def create_temp_coefficient(self, parent):
        """创建温度系数配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="温度系数:", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)

        coeff_frame = tk.Frame(frame, bg='#e8e8e8')
        coeff_frame.pack(fill=tk.X)

        tk.Button(coeff_frame, text="点击选择", font=('Arial', 9), bg='#4472C4',
                 fg='white', width=8, command=self.select_coefficient).pack(side=tk.LEFT)
        tk.Label(coeff_frame, text="或", font=('Arial', 9), bg='#e8e8e8').pack(side=tk.LEFT, padx=5)
        tk.Button(coeff_frame, text="扫码", font=('Arial', 9), bg='#4472C4',
                 fg='white', width=6, command=self.scan_coefficient).pack(side=tk.LEFT)

    def create_sensor_coefficient(self, parent):
        """创建已选择传感器系数"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)

        tk.Label(frame, text="已选择传感器系数:", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)

        # 当前系数文件显示
        self.current_coeff_file_label = tk.Label(frame, text="未选择", font=('Arial', 9),
                                                bg='#e8e8e8', fg='green')
        self.current_coeff_file_label.pack(anchor=tk.W, pady=(2,5))

        # 系数显示区域
        self.coeff_display = tk.Text(frame, height=3, width=20, font=('Arial', 8),
                                    bg='white', state=tk.DISABLED)
        self.coeff_display.pack(fill=tk.X)

        # 初始化显示提示信息
        self.update_coefficient_display("请选择或扫码加载温压系数")
    
    def setup_mock_library(self):
        """设置模拟库"""
        self.is_connected = True
        self.status_indicator.config(fg='green')
    
    def start_data_thread(self):
        """启动数据处理线程"""
        def process_data():
            while True:
                try:
                    if self.real_time_running:
                        if self.use_real_data and self.serial_comm and self.serial_comm.is_connected:
                            # 读取真实串口数据
                            cavity_data = self.read_real_sensor_data()
                            print(f"读取到的原始数据: {cavity_data}")  # 调试信息

                            if cavity_data:
                                temp_cavity = cavity_data.get('temperature_cavity', 0)
                                press_cavity = cavity_data.get('pressure_cavity', 0)
                                print(f"腔长数据 - 温度腔: {temp_cavity}, 压力腔: {press_cavity}")  # 调试信息

                                # 检查是否为无效数据（全FF表示传感器未连接）
                                if temp_cavity >= 65535 or press_cavity >= 65535:
                                    print("检测到无效数据（全FF），传感器可能未连接")  # 调试信息
                                    self.data_queue.put(("data", 19.7, "NA"))
                                    self.data_queue.put(("sensor_status", "传感器信号强度低，不建议使用"))
                                else:
                                    # 尝试使用系数管理器计算温度和压力
                                    temp = None
                                    press = None
                                    use_fallback = True

                                    if self.coefficient_manager.coefficients_loaded:
                                        print(f"系数已加载，开始计算...")
                                        print(f"系数内容: {self.coefficient_manager.coefficients}")
                                        temp = self.coefficient_manager.compute_temperature(temp_cavity)
                                        press = self.coefficient_manager.compute_pressure(temp_cavity, press_cavity, temp)
                                        print(f"系数计算结果 - 温度: {temp}°C, 压力: {press}Pa")  # 调试信息

                                        # 检查计算结果是否有效
                                        if temp is not None and press is not None and temp != 0.0 and press != 0.0:
                                            use_fallback = False
                                        else:
                                            print(f"系数计算结果无效: temp={temp}, press={press}")

                                    # 如果系数计算失败或未加载系数，使用临时计算
                                    if use_fallback:
                                        print("系数未加载或计算失败，使用临时校准计算...")
                                        print(f"输入数据 - 温度腔: {temp_cavity}nm, 压力腔: {press_cavity}nm")

                                        # 基于官方软件表现的正确计算方法
                                        # 官方软件显示: 室温24°C, 手部加热32°C, 变化范围8°C
                                        temp_ref_cavity = 14165.5  # 参考腔长
                                        temp_ref_value = 24.0      # 室温参考值
                                        temp_coefficient = 2.0     # 增大系数，使温度变化更明显

                                        press_ref_cavity = 18750.5  # 参考腔长
                                        press_ref_value = 1.0       # 压力参考值
                                        press_coefficient = 0.1     # 增大系数

                                        # 计算温度和压力
                                        temp = temp_coefficient * (temp_cavity - temp_ref_cavity) + temp_ref_value
                                        press_mmhg = press_coefficient * (press_cavity - press_ref_cavity) + press_ref_value

                                        # 设置合理范围，允许更大变化
                                        temp = max(15.0, min(45.0, temp))    # 允许15-45°C范围
                                        press_mmhg = max(0.0, min(5.0, press_mmhg))  # 允许0-5mmHg范围

                                        print(f"临时计算结果 - 温度: {temp:.1f}°C, 压力: {press_mmhg:.2f}mmHg")
                                        self.data_queue.put(("data", temp, press_mmhg))
                                    else:
                                        # 系数计算成功，转换压力单位 Pa -> mmHg
                                        press_mmhg = press * 0.00750062
                                        print(f"最终数据 - 温度: {temp}°C, 压力: {press_mmhg}mmHg")  # 调试信息
                                        self.data_queue.put(("data", temp, press_mmhg))
                            else:
                                print("读取传感器数据失败")  # 调试信息
                                self.data_queue.put(("data", 19.7, "NA"))
                        else:
                            # 未连接传感器时，不显示数据
                            self.data_queue.put(("sensor_status", "传感器未连接"))

                    time.sleep(0.1)  # 减少延时，提高响应速度
                except Exception as e:
                    print(f"数据处理错误: {e}")
                    # 发生错误时，显示错误状态
                    if self.real_time_running:
                        self.data_queue.put(("sensor_status", f"数据读取错误: {e}"))

        threading.Thread(target=process_data, daemon=True).start()

    def read_real_sensor_data(self):
        """读取真实传感器数据"""
        try:
            if not self.serial_comm or not self.serial_comm.is_connected:
                return None

            # 尝试多种方式获取数据

            # 专注于读取腔长数据，不发送其他查询命令

            # 根据JR-3220DN文档，启用腔长数据传输
            if not hasattr(self, '_cavity_data_enabled'):
                print("启用腔长数据传输...")
                # 发送启用命令 FA070001
                if self.serial_comm.send_command(self.serial_comm.CMD_ENABLE_CAVITY_DATA):
                    print("腔长数据传输启用成功")
                    self._cavity_data_enabled = True

                    # 根据文档：需要添加适当的延迟时间余量
                    print("等待模块响应...")
                    time.sleep(0.2)  # 增加延迟时间

                else:
                    print("腔长数据传输启用失败")
                    return None

            # 连续读取数据流 - 根据文档，模块会连续发送8字节腔长数据
            print("开始连续读取腔长数据流...")

            # 尝试多次读取，因为数据是连续流式传输的
            for attempt in range(10):  # 增加尝试次数
                try:
                    # 读取更多数据，因为可能有多个8字节包
                    data = self.serial_comm.read_response(expected_length=None, timeout=1.0)

                    if data and len(data) > 0:
                        print(f"第{attempt+1}次读取到 {len(data)} 字节: {data.hex()}")

                        # 在数据流中查找所有FEFF帧头
                        pos = 0
                        while pos < len(data) - 7:  # 确保至少有8字节
                            feff_pos = data.find(bytes.fromhex('FEFF'), pos)
                            if feff_pos >= 0 and len(data) >= feff_pos + 8:
                                frame_data = data[feff_pos:feff_pos + 8]
                                print(f"找到腔长帧: {frame_data.hex()}")

                                # 检查是否为无效数据（全FF）
                                if frame_data == bytes.fromhex('FEFFFFFFFFFFFFFF'):
                                    print("检测到无效数据帧（传感器未连接）")
                                    pos = feff_pos + 8
                                    continue

                                # 解析腔长数据
                                parsed_data = self.serial_comm.parse_cavity_data(frame_data)
                                if parsed_data:
                                    print(f"解析成功: {parsed_data}")
                                    return parsed_data

                                pos = feff_pos + 8
                            else:
                                break
                    else:
                        print(f"第{attempt+1}次读取无数据")

                    # 短暂等待后继续读取
                    time.sleep(0.1)

                except Exception as e:
                    print(f"读取数据时出错: {e}")
                    continue

            print("多次尝试后仍未读取到有效腔长数据")

            return None

        except Exception as e:
            print(f"读取传感器数据异常: {e}")
            return None

    def process_queue(self):
        """处理数据队列"""
        def handle_queue():
            try:
                while not self.data_queue.empty():
                    data = self.data_queue.get_nowait()
                    if data[0] == "data":
                        self.update_display(data[1], data[2])
                    elif data[0] == "sensor_status":
                        self.show_sensor_status(data[1])
            except:
                pass

            self.root.after(100, handle_queue)

        handle_queue()

    def show_sensor_status(self, message):
        """显示传感器状态信息"""
        # 可以在这里添加状态栏或弹出提示
        print(f"传感器状态: {message}")
        # 如果需要，可以在界面上显示状态信息

    def toggle_data_collection(self):
        """切换数据采集状态"""
        if self.real_time_running:
            self.real_time_running = False
            self.start_btn.config(text="开始接收数据", bg='#4472C4')
        else:
            # 检查是否已加载温压系数
            if not self.coefficient_manager.has_valid_coefficients():
                messagebox.showwarning("警告",
                    "请先加载温压系数！\n\n请通过以下方式之一加载系数：\n" +
                    "1. 点击右侧'点击选择'按钮选择系数文件\n" +
                    "2. 点击右侧'扫码'按钮扫码获取系数\n" +
                    "3. 连接传感器自动读取芯片系数")
                return

            self.real_time_running = True
            self.start_btn.config(text="停止接收数据", bg='#ff4444')
            # 初始化开始时间
            self.start_time = time.time()
            # 清空之前的数据
            self.time_data = []
            self.temp_data = []
            self.press_data = []
    
    def update_display(self, temp, press):
        """更新显示数据"""
        # 更新数值显示
        self.temp_value.config(text=f"{temp:.1f}")

        # 处理压力值，可能是数字或"NA"
        if isinstance(press, str) and press == "NA":
            self.press_value.config(text="NA")
            press_value = 0  # 图表中显示为0或不显示
        else:
            self.press_value.config(text=f"{press:.1f}")
            press_value = press

        # 更新图表数据 - 改进时间轴逻辑
        current_time = time.time() - self.start_time if hasattr(self, 'start_time') else len(self.time_data)

        # 保存所有历史数据，不删除旧数据
        self.time_data.append(current_time)
        self.temp_data.append(temp)
        self.press_data.append(press_value)

        # 更新图表
        if len(self.time_data) > 1:
            self.temp_line.set_data(self.time_data, self.temp_data)
            self.press_line.set_data(self.time_data, self.press_data)

            # 添加当前数据点标记
            if len(self.time_data) > 0:
                # 清除之前的标记
                for artist in self.ax.collections[:]:
                    if hasattr(artist, '_temp_marker'):
                        artist.remove()
                for artist in self.ax2.collections[:]:
                    if hasattr(artist, '_press_marker'):
                        artist.remove()

                # 添加新的标记
                current_temp_marker = self.ax.scatter([current_time], [temp], c='red', s=50, zorder=5)
                current_press_marker = self.ax2.scatter([current_time], [press], c='red', s=50, zorder=5)
                current_temp_marker._temp_marker = True
                current_press_marker._press_marker = True

            # 调整X轴范围 - 滚动窗口显示最近30秒的数据
            if current_time > 30:
                self.ax.set_xlim(current_time - 30, current_time)
                # 更新X轴标签
                self.ax.set_xlabel(f'时间(最近30秒，当前: {current_time:.1f}s)', fontsize=10)
            else:
                self.ax.set_xlim(0, 30)
                self.ax.set_xlabel('时间(30秒)', fontsize=10)

            # 更新状态文本
            self.temp_status.set_text(f'温度：距长：{temp:.2f}，采样频率：1Hz')
            self.press_status.set_text(f'压力：距长：{press:.2f}，采样频率：1Hz')

            self.canvas.draw_idle()

    # 下拉菜单事件处理函数
    def on_output_change(self, event):
        """输出物理量改变事件"""
        selected = self.output_var.get()
        print(f"输出物理量改变为: {selected}")
        # 这里可以添加具体的处理逻辑

    def on_temp_unit_change(self, event):
        """温度单位改变事件"""
        selected = self.temp_unit_var.get()
        print(f"温度单位改变为: {selected}")
        # 这里可以添加单位转换逻辑

    def on_press_unit_change(self, event):
        """压力单位改变事件"""
        selected = self.press_unit_var.get()
        print(f"压力单位改变为: {selected}")
        # 这里可以添加单位转换逻辑

    def on_chip_change(self, event):
        """芯片连接器改变事件"""
        selected = self.chip_var.get()
        print(f"芯片连接器改变为: {selected}")
        # 这里可以添加芯片连接器处理逻辑

    def on_data_save_change(self, event):
        """数据保存方式改变事件"""
        selected = self.data_save_var.get()
        print(f"数据保存方式改变为: {selected}")
        # 这里可以添加数据保存处理逻辑

    def on_pressure_change(self, event):
        """气压影响设置改变事件"""
        selected = self.pressure_var.get()
        print(f"气压影响设置改变为: {selected}")
        # 这里可以添加气压补偿处理逻辑

    def on_temp_range_change(self, event):
        """温度显示范围改变事件"""
        selected = self.temp_range_var.get()
        print(f"温度显示范围改变为: {selected}")
        # 解析范围并更新图表
        try:
            min_val, max_val = selected.split('-')
            min_val, max_val = int(min_val), int(max_val)
            self.ax.set_ylim(min_val, max_val)
            self.canvas.draw_idle()
        except:
            pass

    def on_press_range_change(self, event):
        """压力显示范围改变事件"""
        selected = self.press_range_var.get()
        print(f"压力显示范围改变为: {selected}")
        # 解析范围并更新图表
        try:
            min_val, max_val = selected.split('-')
            min_val, max_val = int(min_val), int(max_val)
            self.ax2.set_ylim(min_val, max_val)
            self.canvas.draw_idle()
        except:
            pass

    def on_serial_change(self, event):
        """串口选择改变事件"""
        selected_port = self.serial_var.get()
        print(f"串口改变为: {selected_port}")

        # 断开之前的连接
        if self.serial_comm:
            self.serial_comm.disconnect()
            self.serial_comm = None

        if selected_port != "未连接" and selected_port in ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8"]:
            # 尝试连接新的串口
            self.connect_serial_port(selected_port)
        else:
            self.status_indicator.config(fg='red')
            self.is_connected = False
            self.use_real_data = False

    def connect_serial_port(self, port):
        """连接串口"""
        print(f"尝试连接串口: {port}")
        try:
            self.serial_comm = JR3220Serial(port=port, debug=True)
            print(f"JR3220Serial对象已创建")

            if self.serial_comm.connect():
                self.status_indicator.config(fg='green')
                self.is_connected = True
                self.use_real_data = True
                print(f"串口连接成功: {port}")

                # 启用腔长数据传输
                print("启用腔长数据传输...")
                self.serial_comm.enable_cavity_data_transmission()

                # 查询芯片系数
                print("查询芯片系数...")
                self.query_chip_coefficients()

                print(f"成功连接到串口: {port}")
            else:
                self.status_indicator.config(fg='red')
                self.is_connected = False
                self.use_real_data = False
                print(f"连接串口失败: {port}")
        except Exception as e:
            self.status_indicator.config(fg='red')
            self.is_connected = False
            self.use_real_data = False
            print(f"串口连接异常: {e}")
            import traceback
            traceback.print_exc()

    def auto_connect_serial(self):
        """自动连接可用的串口"""
        print("开始自动连接串口...")

        # 尝试常用的串口
        ports_to_try = ["COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM1", "COM2"]

        for port in ports_to_try:
            print(f"尝试连接 {port}...")
            try:
                # 创建临时连接测试
                test_comm = JR3220Serial(port=port, debug=True)
                if test_comm.connect():
                    print(f"成功连接到 {port}")
                    test_comm.disconnect()

                    # 更新界面并连接
                    self.serial_var.set(port)
                    self.connect_serial_port(port)
                    return
                else:
                    print(f"{port} 连接失败")
            except Exception as e:
                print(f"{port} 连接异常: {e}")

        print("未找到可用的串口连接")

    def test_all_ports(self):
        """测试所有串口连接"""
        print("=== 开始测试所有串口 ===")

        # 先列出所有可用串口
        try:
            from jr3220_serial import JR3220Serial
            temp_comm = JR3220Serial(debug=True)
            available_ports = temp_comm.list_available_ports()
            print(f"系统中可用的串口: {available_ports}")
        except Exception as e:
            print(f"获取串口列表失败: {e}")
            available_ports = ["COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8"]

        # 测试每个串口
        for port in available_ports:
            print(f"\n--- 测试 {port} ---")
            try:
                test_comm = JR3220Serial(port=port, debug=True)
                if test_comm.connect():
                    print(f"✅ {port} 连接成功!")
                    test_comm.disconnect()

                    # 自动连接到第一个成功的串口
                    self.serial_var.set(port)
                    self.connect_serial_port(port)
                    print(f"已自动连接到 {port}")
                    return
                else:
                    print(f"❌ {port} 连接失败")
            except Exception as e:
                print(f"❌ {port} 连接异常: {e}")

        print("\n=== 所有串口测试完成，未找到可用连接 ===")

    def test_serial_parameters(self):
        """测试不同的串口参数"""
        print("=== 开始测试不同串口参数 ===")

        # 先断开当前连接
        if self.serial_comm and self.serial_comm.is_connected:
            print("断开当前串口连接...")
            self.serial_comm.disconnect()
            import time
            time.sleep(1)  # 等待端口释放

        # 常用的波特率
        baudrates = [9600, 19200, 38400, 57600, 115200]
        port = "COM3"  # 使用已知的串口

        for baudrate in baudrates:
            print(f"\n--- 测试波特率 {baudrate} ---")
            try:
                import serial
                test_serial = serial.Serial(
                    port=port,
                    baudrate=baudrate,
                    bytesize=serial.EIGHTBITS,
                    parity=serial.PARITY_NONE,
                    stopbits=serial.STOPBITS_ONE,
                    timeout=1.0
                )

                # 清空缓冲区
                test_serial.flushInput()
                test_serial.flushOutput()

                # 尝试多个命令
                commands = [
                    ('FB03', '版本查询'),
                    ('FA070001', '启用腔长数据'),
                    ('F0EE', '芯片系数查询'),
                    ('FB57', '环境压力查询1'),
                    ('FB56', '环境压力查询2')
                ]

                found_response = False
                for cmd_hex, cmd_name in commands:
                    cmd = bytes.fromhex(cmd_hex)
                    test_serial.write(cmd)
                    print(f"发送{cmd_name}命令: {cmd.hex()}")

                    # 等待响应
                    import time
                    time.sleep(0.2)  # 增加等待时间

                    if test_serial.in_waiting > 0:
                        response = test_serial.read(test_serial.in_waiting)
                        print(f"✅ 波特率 {baudrate} 对{cmd_name}有响应: {response.hex()}")
                        found_response = True
                        break
                    else:
                        print(f"  - {cmd_name}无响应")

                if found_response:
                    test_serial.close()
                    print(f"找到正确的波特率: {baudrate}")
                    self.connect_with_baudrate(port, baudrate)
                    return baudrate
                else:
                    print(f"❌ 波特率 {baudrate} 所有命令都无响应")

                test_serial.close()

            except Exception as e:
                print(f"❌ 波特率 {baudrate} 测试异常: {e}")

        print("\n=== 所有波特率测试完成，未找到响应 ===")

        # 重新连接原来的串口
        print("重新连接串口...")
        self.connect_serial_port(port)

        return None

    def connect_with_baudrate(self, port, baudrate):
        """使用指定波特率连接串口"""
        try:
            print(f"使用波特率 {baudrate} 连接 {port}...")

            # 创建新的JR3220Serial对象，使用指定波特率
            from jr3220_serial import JR3220Serial

            # 临时修改JR3220Serial的波特率
            original_baudrate = JR3220Serial.BAUDRATE
            JR3220Serial.BAUDRATE = baudrate

            # 创建新连接
            self.serial_comm = JR3220Serial(debug=True)
            success = self.serial_comm.connect(port)

            # 恢复原始波特率
            JR3220Serial.BAUDRATE = original_baudrate

            if success:
                print(f"✅ 使用波特率 {baudrate} 连接成功")
                self.is_connected = True
                self.status_indicator.config(fg='green')

                # 尝试查询设备信息验证连接
                version = self.serial_comm.query_version()
                if version:
                    print(f"设备版本: {version}")

                return True
            else:
                print(f"❌ 使用波特率 {baudrate} 连接失败")
                return False

        except Exception as e:
            print(f"连接异常: {e}")
            return False

    def enable_simulation_mode(self):
        """启用模拟数据模式"""
        print("=== 启用模拟数据模式 ===")
        print("由于硬件通信问题，启用模拟数据模式以演示程序功能")

        # 设置模拟模式标志
        self.use_real_data = False
        self.is_connected = True
        self.status_indicator.config(fg='orange')  # 橙色表示模拟模式

        # 显示模拟模式提示
        import tkinter.messagebox as msgbox
        msgbox.showinfo("模拟数据模式",
                       "已启用模拟数据模式！\n\n"
                       "- 状态指示灯变为橙色\n"
                       "- 点击'开始接收数据'查看模拟效果\n"
                       "- 数据会有真实的波动\n\n"
                       "解决硬件问题后可切换回真实数据模式")

    def start_raw_data_listener(self):
        """启动原始数据监听器"""
        print("=== 启动原始数据监听器 ===")
        print("监听COM3端口的所有数据流...")
        print("请在您的其他软件中操作，我们来捕获数据包")

        if not self.serial_comm or not self.serial_comm.is_connected:
            print("串口未连接，尝试连接...")
            self.connect_serial_port("COM3")

        # 启动监听线程
        import threading
        def listen_raw_data():
            try:
                buffer = b''
                print("开始监听原始数据...")

                for i in range(100):  # 监听100次
                    if self.serial_comm and self.serial_comm.serial_conn:
                        # 读取所有可用数据
                        if self.serial_comm.serial_conn.in_waiting > 0:
                            data = self.serial_comm.serial_conn.read(self.serial_comm.serial_conn.in_waiting)
                            if data:
                                buffer += data
                                print(f"[{i:03d}] 接收到数据: {len(data)}字节 - {data.hex().upper()}")

                                # 尝试解析ASCII字符
                                try:
                                    ascii_str = data.decode('ascii', errors='ignore')
                                    if ascii_str.strip():
                                        print(f"      ASCII: '{ascii_str.strip()}'")
                                except:
                                    pass

                    import time
                    time.sleep(0.1)

                print(f"\n=== 监听完成，总共接收 {len(buffer)} 字节 ===")
                if buffer:
                    print(f"完整数据: {buffer.hex().upper()}")

                    # 尝试找到重复的模式
                    if len(buffer) > 16:
                        print("\n分析数据模式...")
                        for i in range(0, len(buffer)-8, 8):
                            chunk = buffer[i:i+8]
                            print(f"数据块 {i//8+1}: {chunk.hex().upper()}")
                else:
                    print("未接收到任何数据")

            except Exception as e:
                print(f"监听异常: {e}")

        listener_thread = threading.Thread(target=listen_raw_data, daemon=True)
        listener_thread.start()

        import tkinter.messagebox as msgbox
        msgbox.showinfo("原始数据监听",
                       "已启动原始数据监听！\n\n"
                       "请在您的其他软件中:\n"
                       "1. 点击'温度收零'或其他按钮\n"
                       "2. 观察数据变化\n"
                       "3. 查看终端输出的数据包\n\n"
                       "这将帮助我们分析正确的协议格式")

    def query_chip_coefficients(self):
        """查询芯片系数"""
        if not self.serial_comm or not self.serial_comm.is_connected:
            return

        try:
            print("发送芯片系数查询命令...")
            chip_data = self.serial_comm.query_chip_coefficient()

            if chip_data and chip_data.get('status') == 'success':
                coeff_text = chip_data.get('coefficient_text', '')
                success, message = self.coefficient_manager.load_from_chip_data(coeff_text)
                if success:
                    self.update_coefficient_display(f"芯片系数:\n{coeff_text}")
                    print("✅ 芯片系数加载成功")
                else:
                    print(f"❌ 芯片系数加载失败: {message}")
                    self.setup_fallback_calculation()
            elif chip_data and chip_data.get('status') == 'no_chip':
                print("⚠️ 未检测到系数芯片，将使用临时计算")
                self.update_coefficient_display("未检测到系数芯片\n将使用基于观测数据的临时计算")
                self.setup_fallback_calculation()
            else:
                print("❌ 芯片系数查询失败，将使用临时计算")
                self.update_coefficient_display("芯片系数查询失败\n将使用基于观测数据的临时计算")
                self.setup_fallback_calculation()
        except Exception as e:
            print(f"❌ 查询芯片系数异常: {e}")
            self.setup_fallback_calculation()

    def setup_fallback_calculation(self):
        """设置备用计算方法"""
        print("设置临时计算参数...")
        # 这里可以设置一些标志，表明使用临时计算
        self.coefficient_manager.coefficients_loaded = False
        print("✅ 临时计算已准备就绪")

    def select_coefficient(self):
        """选择温度系数"""
        filename = filedialog.askopenfilename(
            title="选择系数文件",
            filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt"), ("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                # 尝试加载系数文件
                success, message = self.coefficient_manager.load_from_file(filename)
                file_basename = os.path.basename(filename)
                if success:
                    self.update_coefficient_display(f"已加载系数文件:\n{filename}\n\n{message}")
                    self.current_coeff_file_label.config(text=file_basename)
                    print(f"系数文件加载成功: {filename}")
                    messagebox.showinfo("成功", f"系数文件加载成功!\n{message}")
                else:
                    # 如果JSON加载失败，尝试作为文本文件读取
                    with open(filename, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 尝试解析为芯片数据格式
                    success2, message2 = self.coefficient_manager.load_from_chip_data(content)
                    if success2:
                        self.update_coefficient_display(f"已解析系数文件:\n{filename}\n\n{content[:200]}...")
                        self.current_coeff_file_label.config(text=file_basename)
                        print(f"系数文件解析成功: {filename}")
                        messagebox.showinfo("成功", f"系数文件解析成功!\n{message2}")
                    else:
                        # 仍然显示文件内容，但提示解析失败
                        self.update_coefficient_display(f"文件内容:\n{filename}\n\n{content[:200]}...")
                        self.current_coeff_file_label.config(text="解析失败")
                        print(f"系数文件解析失败: {message2}")
                        messagebox.showwarning("警告", f"系数文件格式不正确:\n{message2}\n\n文件内容已显示，请检查格式。")

            except Exception as e:
                messagebox.showerror("错误", f"无法读取文件: {e}")
                print(f"读取系数文件失败: {e}")

    def scan_coefficient(self):
        """扫码获取温度系数"""
        # 实际扫码功能需要连接扫码设备
        messagebox.showinfo("扫码功能", "请连接扫码设备后使用此功能")
        print("扫码功能需要硬件支持")

    def update_coefficient_display(self, text):
        """更新系数显示区域"""
        self.coeff_display.config(state=tk.NORMAL)
        self.coeff_display.delete(1.0, tk.END)
        self.coeff_display.insert(1.0, text)
        self.coeff_display.config(state=tk.DISABLED)

    def temp_calibration(self):
        """温度校零"""
        result = messagebox.askyesno("温度校零", "确定要进行温度校零吗？\n请确保传感器处于标准温度环境中。")
        if result:
            # 模拟校零过程
            messagebox.showinfo("校零中", "正在进行温度校零，请稍候...")
            # 这里可以添加实际的校零逻辑
            print("执行温度校零")
            messagebox.showinfo("校零完成", "温度校零完成！")

    def press_calibration(self):
        """压力校零"""
        result = messagebox.askyesno("压力校零", "确定要进行压力校零吗？\n请确保传感器处于标准压力环境中。")
        if result:
            # 模拟校零过程
            messagebox.showinfo("校零中", "正在进行压力校零，请稍候...")
            # 这里可以添加实际的校零逻辑
            print("执行压力校零")
            messagebox.showinfo("校零完成", "压力校零完成！")

def main():
    """主函数"""
    root = tk.Tk()
    app = X120GUIV2(root)
    root.mainloop()

if __name__ == "__main__":
    main()
