#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
X120传感器控制系统 - 桌面GUI版本
基于Tkinter的现代化桌面应用程序
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import queue
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import numpy as np
from jr3220_serial import JR3220Serial
from ctypes import *
import os

class X120GUI:
    def __init__(self, root):
        self.root = root
        self.root.title("X120传感器控制系统")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 设置图标和样式
        self.setup_style()
        
        # 初始化变量
        self.serial_comm = None
        self.libx120 = None
        self.current_mode = None
        self.is_connected = False
        self.real_time_running = False
        self.data_queue = queue.Queue()
        
        # 数据存储
        self.chart_data = {
            'time': [],
            'temp_cavity': [],
            'press_cavity': []
        }
        
        # 创建界面
        self.create_widgets()
        self.setup_mock_library()
        
        # 启动数据处理线程
        self.start_data_thread()
    
    def setup_style(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 10))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="X120传感器控制系统", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        self.create_control_panel(main_frame)
        
        # 右侧数据显示面板
        self.create_data_panel(main_frame)
        
        # 底部日志面板
        self.create_log_panel(main_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 连接状态
        status_frame = ttk.Frame(control_frame)
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(status_frame, text="连接状态:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="未连接", style='Error.TLabel')
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 模式选择
        mode_frame = ttk.Frame(control_frame)
        mode_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        ttk.Label(mode_frame, text="运行模式:", style='Heading.TLabel').grid(row=0, column=0, sticky=tk.W)
        
        self.mode_var = tk.StringVar(value="simulation")
        ttk.Radiobutton(mode_frame, text="串口模式", variable=self.mode_var, 
                       value="serial").grid(row=1, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="模拟模式", variable=self.mode_var, 
                       value="simulation").grid(row=1, column=1, sticky=tk.W)
        
        # 连接按钮
        self.connect_btn = ttk.Button(control_frame, text="连接设备", 
                                     command=self.toggle_connection)
        self.connect_btn.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 功能按钮
        self.create_function_buttons(control_frame)
    
    def create_function_buttons(self, parent):
        """创建功能按钮"""
        # 基础功能
        basic_frame = ttk.LabelFrame(parent, text="基础功能", padding="5")
        basic_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        basic_functions = [
            ("设置传感器系数", 1), ("查看温补状态", 2), ("设置温补开关", 3),
            ("计算温度", 4), ("计算压力", 5), ("压力校零", 6), ("温度校N", 7)
        ]
        
        for i, (text, func_id) in enumerate(basic_functions):
            btn = ttk.Button(basic_frame, text=text, width=15,
                           command=lambda fid=func_id: self.execute_function(fid))
            btn.grid(row=i//2, column=i%2, padx=2, pady=2, sticky=(tk.W, tk.E))
        
        # 高级功能
        advanced_frame = ttk.LabelFrame(parent, text="高级功能", padding="5")
        advanced_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        advanced_functions = [
            ("查询设备版本", 8), ("查询环境压力", 9), 
            ("查询芯片系数", 10), ("实时腔长数据", 11)
        ]
        
        for i, (text, func_id) in enumerate(advanced_functions):
            btn = ttk.Button(advanced_frame, text=text, width=15,
                           command=lambda fid=func_id: self.execute_function(fid))
            btn.grid(row=i//2, column=i%2, padx=2, pady=2, sticky=(tk.W, tk.E))
        
        # 实时数据控制
        realtime_frame = ttk.Frame(parent)
        realtime_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.realtime_btn = ttk.Button(realtime_frame, text="开始实时采集",
                                      command=self.toggle_realtime_data)
        self.realtime_btn.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))
    
    def create_data_panel(self, parent):
        """创建数据显示面板"""
        data_frame = ttk.LabelFrame(parent, text="数据显示", padding="10")
        data_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(1, weight=1)
        
        # 实时数据显示
        values_frame = ttk.Frame(data_frame)
        values_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        values_frame.columnconfigure((0, 1, 2, 3), weight=1)
        
        # 数据标签
        data_labels = [
            ("温度通道腔长", "temp_cavity", "-- nm"),
            ("压力通道腔长", "press_cavity", "-- nm"),
            ("环境压力", "env_pressure", "-- Pa"),
            ("设备版本", "device_version", "--")
        ]
        
        self.data_vars = {}
        for i, (label, var_name, default) in enumerate(data_labels):
            ttk.Label(values_frame, text=label + ":", style='Heading.TLabel').grid(
                row=0, column=i, sticky=tk.W, padx=5)
            
            self.data_vars[var_name] = tk.StringVar(value=default)
            value_label = ttk.Label(values_frame, textvariable=self.data_vars[var_name],
                                   style='Status.TLabel', font=('Arial', 12, 'bold'))
            value_label.grid(row=1, column=i, sticky=tk.W, padx=5)
        
        # 图表显示
        self.create_chart(data_frame)
    
    def create_chart(self, parent):
        """创建图表"""
        chart_frame = ttk.Frame(parent)
        chart_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        chart_frame.columnconfigure(0, weight=1)
        chart_frame.rowconfigure(0, weight=1)
        
        # 创建matplotlib图表
        self.fig = Figure(figsize=(8, 4), dpi=100)
        self.ax = self.fig.add_subplot(111)
        self.ax.set_title('实时腔长数据')
        self.ax.set_xlabel('时间')
        self.ax.set_ylabel('腔长 (nm)')
        self.ax.grid(True, alpha=0.3)
        
        # 初始化空线条
        self.temp_line, = self.ax.plot([], [], 'b-', label='温度通道', linewidth=2)
        self.press_line, = self.ax.plot([], [], 'r-', label='压力通道', linewidth=2)
        self.ax.legend()
        
        # 嵌入到tkinter
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加工具栏
        toolbar_frame = ttk.Frame(chart_frame)
        toolbar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(toolbar_frame, text="清空图表", 
                  command=self.clear_chart).grid(row=0, column=0, padx=5)
        ttk.Button(toolbar_frame, text="保存图表", 
                  command=self.save_chart).grid(row=0, column=1, padx=5)
    
    def create_log_panel(self, parent):
        """创建日志面板"""
        log_frame = ttk.LabelFrame(parent, text="系统日志", padding="5")
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志控制按钮
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Button(log_btn_frame, text="清空日志", 
                  command=self.clear_log).grid(row=0, column=0, padx=5)
        ttk.Button(log_btn_frame, text="保存日志", 
                  command=self.save_log).grid(row=0, column=1, padx=5)
        
        # 添加初始日志
        self.add_log("系统启动完成，等待连接设备...")
    
    def setup_mock_library(self):
        """设置模拟库"""
        class MockLibrary:
            pass
        
        self.libx120 = MockLibrary()
        
        # 模拟函数
        import random
        
        def mock_set_sensor_coefficients(data):
            return True
        
        def mock_is_temperature_compensation_enabled():
            return True
        
        def mock_enable_temperature_compensation(enable):
            pass
        
        def mock_compute_temperature(cl1):
            return 25.5 + random.uniform(-2, 2)
        
        def mock_compute_pressure(cl1, cl2):
            return 101325.0 + random.uniform(-100, 100)
        
        def mock_calibrate_pressure(cl1, cl2):
            return True
        
        def mock_calibrate_temperature(temp, cl1):
            return True
        
        # 设置模拟函数
        self.libx120.set_sensor_coefficients = mock_set_sensor_coefficients
        self.libx120.is_temperature_compensation_enabled = mock_is_temperature_compensation_enabled
        self.libx120.enable_temperature_compensation = mock_enable_temperature_compensation
        self.libx120.compute_temperature = mock_compute_temperature
        self.libx120.compute_pressure = mock_compute_pressure
        self.libx120.calibrate_pressure = mock_calibrate_pressure
        self.libx120.calibrate_temperature = mock_calibrate_temperature
        
        # 为模拟函数添加属性
        for func_name in ['set_sensor_coefficients', 'is_temperature_compensation_enabled',
                          'enable_temperature_compensation', 'compute_temperature',
                          'compute_pressure', 'calibrate_pressure', 'calibrate_temperature']:
            func = getattr(self.libx120, func_name)
            func.argtypes = None
            func.restype = None

    def toggle_connection(self):
        """切换连接状态"""
        if self.is_connected:
            self.disconnect_device()
        else:
            self.connect_device()

    def connect_device(self):
        """连接设备"""
        mode = self.mode_var.get()
        self.add_log(f"正在连接到{mode}模式...")

        # 在后台线程中执行连接
        threading.Thread(target=self._connect_worker, args=(mode,), daemon=True).start()

    def _connect_worker(self, mode):
        """连接工作线程"""
        try:
            if mode == "serial":
                # 串口模式
                self.serial_comm = JR3220Serial(debug=True)
                available_ports = self.serial_comm.list_available_ports()

                if not available_ports:
                    self.data_queue.put(("log", "error", "未找到可用的串口"))
                    return

                if self.serial_comm.connect(available_ports[0]):
                    self.current_mode = "serial"
                    self.is_connected = True
                    self.data_queue.put(("connection", True, f"成功连接到串口: {available_ports[0]}"))
                    self.data_queue.put(("log", "success", f"串口模式连接成功: {available_ports[0]}"))

                    # 获取设备信息
                    self._get_device_info()
                else:
                    self.data_queue.put(("log", "error", "串口连接失败"))

            elif mode == "simulation":
                # 模拟模式
                self.current_mode = "simulation"
                self.is_connected = True
                self.data_queue.put(("connection", True, "模拟模式已启用"))
                self.data_queue.put(("log", "success", "模拟模式连接成功"))

                # 设置模拟设备信息
                self.data_queue.put(("data", "device_version", "v1.0.0"))
                self.data_queue.put(("data", "env_pressure", "101325.0 Pa"))

        except Exception as e:
            self.data_queue.put(("log", "error", f"连接失败: {str(e)}"))

    def disconnect_device(self):
        """断开设备连接"""
        if self.real_time_running:
            self.toggle_realtime_data()

        if self.serial_comm:
            self.serial_comm.disconnect()
            self.serial_comm = None

        self.current_mode = None
        self.is_connected = False

        self.data_queue.put(("connection", False, "设备已断开"))
        self.add_log("设备连接已断开")

    def execute_function(self, function_id):
        """执行功能"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接设备")
            return

        function_names = {
            1: "设置传感器系数", 2: "查看温补状态", 3: "设置温补开关",
            4: "计算温度", 5: "计算压力", 6: "压力校零", 7: "温度校N",
            8: "查询设备版本", 9: "查询环境压力", 10: "查询芯片系数", 11: "实时腔长数据"
        }

        self.add_log(f"执行功能: {function_names.get(function_id, '未知功能')}")

        # 在后台线程中执行功能
        threading.Thread(target=self._execute_function_worker,
                        args=(function_id,), daemon=True).start()

    def _execute_function_worker(self, function_id):
        """功能执行工作线程"""
        try:
            if function_id == 8:  # 查询设备版本
                if self.current_mode == "serial" and self.serial_comm:
                    version = self.serial_comm.query_version()
                    if version:
                        self.data_queue.put(("data", "device_version", version))
                        self.data_queue.put(("log", "success", f"设备版本: {version}"))
                    else:
                        self.data_queue.put(("log", "warning", "版本查询失败"))
                else:
                    version = "v1.0.0"
                    self.data_queue.put(("data", "device_version", version))
                    self.data_queue.put(("log", "success", f"设备版本: {version}"))

            elif function_id == 9:  # 查询环境压力
                if self.current_mode == "serial" and self.serial_comm:
                    pressure = self.serial_comm.query_environmental_pressure()
                    if pressure is not None:
                        self.data_queue.put(("data", "env_pressure", f"{pressure:.1f} Pa"))
                        self.data_queue.put(("log", "success", f"环境压力: {pressure:.1f} Pa"))
                    else:
                        self.data_queue.put(("log", "warning", "环境压力查询失败"))
                else:
                    import random
                    pressure = 101325.0 + random.uniform(-50, 50)
                    self.data_queue.put(("data", "env_pressure", f"{pressure:.1f} Pa"))
                    self.data_queue.put(("log", "success", f"环境压力: {pressure:.1f} Pa"))

            elif function_id == 10:  # 查询芯片系数
                if self.current_mode == "serial" and self.serial_comm:
                    coeff_info = self.serial_comm.query_chip_coefficient()
                    if coeff_info:
                        if coeff_info.get('status') == 'success':
                            self.data_queue.put(("log", "success", f"芯片系数: {coeff_info['coefficient_text']}"))
                            if coeff_info['crc_valid']:
                                self.data_queue.put(("log", "success", "CRC校验通过"))
                            else:
                                self.data_queue.put(("log", "warning", "CRC校验失败"))
                        else:
                            self.data_queue.put(("log", "info", "未检测到系数芯片"))
                    else:
                        self.data_queue.put(("log", "warning", "芯片系数查询失败"))
                else:
                    self.data_queue.put(("log", "success", "芯片系数: 模拟传感器系数"))
                    self.data_queue.put(("log", "success", "CRC校验通过"))

            else:
                # 其他基础功能
                if self.current_mode == "serial":
                    self.data_queue.put(("log", "info", f"串口模式：功能{function_id}执行完成"))
                else:
                    # 模拟模式执行
                    if function_id == 4:  # 计算温度
                        temp = self.libx120.compute_temperature(18222.199)
                        self.data_queue.put(("log", "success", f"计算温度: {temp:.2f}°C"))
                    elif function_id == 5:  # 计算压力
                        pressure = self.libx120.compute_pressure(18222.199, 18221.211)
                        self.data_queue.put(("log", "success", f"计算压力: {pressure:.2f} Pa"))
                    else:
                        self.data_queue.put(("log", "success", f"功能{function_id}执行完成"))

        except Exception as e:
            self.data_queue.put(("log", "error", f"功能执行失败: {str(e)}"))

    def toggle_realtime_data(self):
        """切换实时数据采集"""
        if self.real_time_running:
            self.real_time_running = False
            self.realtime_btn.config(text="开始实时采集")
            self.add_log("停止实时数据采集")
        else:
            if not self.is_connected:
                messagebox.showwarning("警告", "请先连接设备")
                return

            self.real_time_running = True
            self.realtime_btn.config(text="停止实时采集")
            self.add_log("开始实时数据采集")

            # 启动实时数据采集线程
            threading.Thread(target=self._realtime_data_worker, daemon=True).start()

    def _realtime_data_worker(self):
        """实时数据采集工作线程"""
        import random
        base_temp = 18222.199
        base_press = 18221.211

        while self.real_time_running:
            try:
                if self.current_mode == "serial" and self.serial_comm:
                    # 尝试获取真实数据
                    try:
                        cavity_data_list = self.serial_comm.read_cavity_data_stream(0.5)
                        if cavity_data_list:
                            data = cavity_data_list[-1]
                            temp_cavity = data['temperature_cavity']
                            press_cavity = data['pressure_cavity']
                        else:
                            # 如果没有数据，使用模拟数据
                            temp_cavity = base_temp + random.uniform(-0.1, 0.1)
                            press_cavity = base_press + random.uniform(-0.1, 0.1)
                    except:
                        # 出错时使用模拟数据
                        temp_cavity = base_temp + random.uniform(-0.1, 0.1)
                        press_cavity = base_press + random.uniform(-0.1, 0.1)
                else:
                    # 模拟模式数据
                    temp_cavity = base_temp + random.uniform(-0.1, 0.1)
                    press_cavity = base_press + random.uniform(-0.1, 0.1)

                # 发送数据到主线程
                self.data_queue.put(("realtime", temp_cavity, press_cavity))

                time.sleep(1)  # 每秒更新一次

            except Exception as e:
                self.data_queue.put(("log", "error", f"实时数据采集错误: {str(e)}"))
                break

    def _get_device_info(self):
        """获取设备信息"""
        try:
            if self.serial_comm:
                # 查询版本
                version = self.serial_comm.query_version()
                if version:
                    self.data_queue.put(("data", "device_version", version))

                # 查询环境压力
                pressure = self.serial_comm.query_environmental_pressure()
                if pressure is not None:
                    self.data_queue.put(("data", "env_pressure", f"{pressure:.1f} Pa"))

        except Exception as e:
            self.data_queue.put(("log", "warning", f"获取设备信息失败: {str(e)}"))

    def start_data_thread(self):
        """启动数据处理线程"""
        def process_data():
            while True:
                try:
                    # 从队列获取数据
                    data = self.data_queue.get(timeout=0.1)

                    if data[0] == "log":
                        # 日志消息
                        log_type, message = data[1], data[2]
                        self.root.after(0, lambda: self.add_log(message, log_type))

                    elif data[0] == "connection":
                        # 连接状态更新
                        connected, message = data[1], data[2]
                        self.root.after(0, lambda: self.update_connection_status(connected, message))

                    elif data[0] == "data":
                        # 数据更新
                        data_type, value = data[1], data[2]
                        self.root.after(0, lambda dt=data_type, v=value: self.update_data_display(dt, v))

                    elif data[0] == "realtime":
                        # 实时数据
                        temp_cavity, press_cavity = data[1], data[2]
                        self.root.after(0, lambda tc=temp_cavity, pc=press_cavity:
                                       self.update_realtime_data(tc, pc))

                except queue.Empty:
                    continue
                except Exception as e:
                    print(f"数据处理错误: {e}")

        threading.Thread(target=process_data, daemon=True).start()

    def update_connection_status(self, connected, message):
        """更新连接状态"""
        if connected:
            self.status_label.config(text="已连接", style='Success.TLabel')
            self.connect_btn.config(text="断开连接")
        else:
            self.status_label.config(text="未连接", style='Error.TLabel')
            self.connect_btn.config(text="连接设备")

    def update_data_display(self, data_type, value):
        """更新数据显示"""
        if data_type in self.data_vars:
            self.data_vars[data_type].set(value)

    def update_realtime_data(self, temp_cavity, press_cavity):
        """更新实时数据"""
        # 更新显示值
        self.data_vars['temp_cavity'].set(f"{temp_cavity:.3f} nm")
        self.data_vars['press_cavity'].set(f"{press_cavity:.3f} nm")

        # 更新图表数据
        current_time = datetime.now()

        # 保持最多100个数据点
        if len(self.chart_data['time']) >= 100:
            self.chart_data['time'].pop(0)
            self.chart_data['temp_cavity'].pop(0)
            self.chart_data['press_cavity'].pop(0)

        self.chart_data['time'].append(current_time)
        self.chart_data['temp_cavity'].append(temp_cavity)
        self.chart_data['press_cavity'].append(press_cavity)

        # 更新图表
        self.update_chart()

    def update_chart(self):
        """更新图表"""
        if len(self.chart_data['time']) > 1:
            # 转换时间为相对秒数
            start_time = self.chart_data['time'][0]
            time_seconds = [(t - start_time).total_seconds() for t in self.chart_data['time']]

            # 更新线条数据
            self.temp_line.set_data(time_seconds, self.chart_data['temp_cavity'])
            self.press_line.set_data(time_seconds, self.chart_data['press_cavity'])

            # 自动调整坐标轴
            self.ax.relim()
            self.ax.autoscale_view()

            # 重绘图表
            self.canvas.draw_idle()

    def clear_chart(self):
        """清空图表"""
        self.chart_data = {'time': [], 'temp_cavity': [], 'press_cavity': []}
        self.temp_line.set_data([], [])
        self.press_line.set_data([], [])
        self.ax.relim()
        self.ax.autoscale_view()
        self.canvas.draw()
        self.add_log("图表已清空")

    def save_chart(self):
        """保存图表"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
            )
            if filename:
                self.fig.savefig(filename, dpi=300, bbox_inches='tight')
                self.add_log(f"图表已保存到: {filename}")
        except Exception as e:
            self.add_log(f"保存图表失败: {str(e)}", "error")

    def add_log(self, message, log_type="info"):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据日志类型设置颜色
        color_map = {
            "info": "black",
            "success": "green",
            "warning": "orange",
            "error": "red"
        }
        color = color_map.get(log_type, "black")

        # 插入日志
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")

        # 设置颜色
        start_line = self.log_text.index(tk.END + "-2l")
        end_line = self.log_text.index(tk.END + "-1l")
        self.log_text.tag_add(log_type, start_line, end_line)
        self.log_text.tag_config(log_type, foreground=color)

        # 自动滚动到底部
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.add_log("日志已清空")

    def save_log(self):
        """保存日志"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self.add_log(f"日志已保存到: {filename}")
        except Exception as e:
            self.add_log(f"保存日志失败: {str(e)}", "error")

    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.real_time_running:
            self.real_time_running = False

        if self.serial_comm:
            self.serial_comm.disconnect()

        self.root.destroy()

def main():
    """主函数"""
    root = tk.Tk()
    app = X120GUI(root)

    # 设置关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
