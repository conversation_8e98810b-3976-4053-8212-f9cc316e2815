#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试右侧面板显示
"""

import tkinter as tk
from tkinter import ttk

class TestRightPanel:
    def __init__(self, root):
        self.root = root
        self.root.title("测试右侧面板")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        self.create_widgets()
    
    def create_widgets(self):
        # 主框架
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建左右分割的主要区域
        content_frame = tk.Frame(main_frame, bg='#f0f0f0')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧内容区域
        left_frame = tk.Frame(content_frame, bg='#f0f0f0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 左侧内容
        tk.Label(left_frame, text="左侧内容区域", font=('Arial', 16), bg='#f0f0f0').pack(pady=50)
        
        # 右侧参数配置区域
        self.create_right_panel(content_frame)
    
    def create_right_panel(self, parent):
        """创建右侧参数配置面板"""
        # 创建右侧面板框架
        right_panel = tk.Frame(parent, bg='#e8e8e8', width=250, relief=tk.RAISED, bd=2)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_panel.pack_propagate(False)
        
        # 参数配置标题
        tk.Label(right_panel, text="参数配置", font=('Arial', 14, 'bold'), 
                bg='#e8e8e8').pack(pady=(10, 10))
        
        # 串口配置
        self.create_serial_config(right_panel)
        
        # 输出物理量配置
        self.create_output_config(right_panel)
        
        # 温度输出单位配置
        self.create_temp_unit_config(right_panel)
        
        # 压力输出单位配置
        self.create_press_unit_config(right_panel)
        
        # 芯片连接器配置
        self.create_chip_config(right_panel)
        
        # 数据保存配置
        self.create_data_save_config(right_panel)
        
        # 温补开关
        self.create_temp_compensation(right_panel)
        
        # 温度系数配置
        self.create_temp_coefficient(right_panel)
        
        # 已选择传感器系数
        self.create_sensor_coefficient(right_panel)
    
    def create_serial_config(self, parent):
        """创建串口配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="串口", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        
        serial_frame = tk.Frame(frame, bg='#e8e8e8')
        serial_frame.pack(fill=tk.X)
        
        # 串口选择
        self.serial_var = tk.StringVar(value="COM3")
        serial_combo = ttk.Combobox(serial_frame, textvariable=self.serial_var,
                                   values=["COM1", "COM2", "COM3", "COM4"], width=15)
        serial_combo.pack(side=tk.LEFT)
        
        # 连接状态指示灯
        self.status_indicator = tk.Label(serial_frame, text="●", font=('Arial', 16),
                                        fg='green', bg='#e8e8e8')
        self.status_indicator.pack(side=tk.RIGHT)
    
    def create_output_config(self, parent):
        """创建输出物理量配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="输出物理量", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.output_var = tk.StringVar(value="温度&压力")
        output_combo = ttk.Combobox(frame, textvariable=self.output_var,
                                   values=["温度&压力", "温度", "压力"], width=15, state="readonly")
        output_combo.pack(fill=tk.X)
    
    def create_temp_unit_config(self, parent):
        """创建温度输出单位配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="温度输出单位", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.temp_unit_var = tk.StringVar(value="温度(℃)")
        temp_unit_combo = ttk.Combobox(frame, textvariable=self.temp_unit_var,
                                      values=["温度(℃)", "华氏度(℉)", "开尔文(K)"], width=15, state="readonly")
        temp_unit_combo.pack(fill=tk.X)
    
    def create_press_unit_config(self, parent):
        """创建压力输出单位配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="压力输出单位", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.press_unit_var = tk.StringVar(value="压力(mmHg)")
        press_unit_combo = ttk.Combobox(frame, textvariable=self.press_unit_var,
                                       values=["压力(mmHg)", "PSI", "压力(kPa)", "压力(bar)", "压力(Pa)"], 
                                       width=15, state="readonly")
        press_unit_combo.pack(fill=tk.X)
    
    def create_chip_config(self, parent):
        """创建芯片连接器配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="芯片连接器", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.chip_var = tk.StringVar(value="无芯片连接器")
        chip_combo = ttk.Combobox(frame, textvariable=self.chip_var,
                                 values=["无芯片连接器", "存在芯片连接器"], width=15, state="readonly")
        chip_combo.pack(fill=tk.X)
    
    def create_data_save_config(self, parent):
        """创建数据保存配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="数据保存方式", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        self.data_save_var = tk.StringVar(value="不保存数据")
        data_save_combo = ttk.Combobox(frame, textvariable=self.data_save_var,
                                      values=["不保存数据", "保存到CSV", "保存到Excel", "保存到TXT"], width=15, state="readonly")
        data_save_combo.pack(fill=tk.X)
    
    def create_temp_compensation(self, parent):
        """创建温补开关"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=10, padx=10)
        
        # 温补开关
        switch_frame = tk.Frame(frame, bg='#e8e8e8')
        switch_frame.pack(fill=tk.X)
        
        self.temp_comp_var = tk.BooleanVar(value=True)
        switch_bg = tk.Frame(switch_frame, bg='#ff69b4', width=60, height=25)
        switch_bg.pack(side=tk.LEFT)
        switch_bg.pack_propagate(False)
        
        tk.Label(switch_bg, text="●", font=('Arial', 16), fg='white', bg='#ff69b4').pack(side=tk.RIGHT)
        
        tk.Label(switch_frame, text="是否开启压力温补", font=('Arial', 10), 
                bg='#e8e8e8').pack(side=tk.LEFT, padx=(10, 0))
    
    def create_temp_coefficient(self, parent):
        """创建温度系数配置"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="温度系数:", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        
        coeff_frame = tk.Frame(frame, bg='#e8e8e8')
        coeff_frame.pack(fill=tk.X)
        
        tk.Button(coeff_frame, text="点击选择", font=('Arial', 9), bg='#4472C4', 
                 fg='white', width=8).pack(side=tk.LEFT)
        tk.Label(coeff_frame, text="或", font=('Arial', 9), bg='#e8e8e8').pack(side=tk.LEFT, padx=5)
        tk.Button(coeff_frame, text="扫码", font=('Arial', 9), bg='#4472C4', 
                 fg='white', width=6).pack(side=tk.LEFT)
    
    def create_sensor_coefficient(self, parent):
        """创建已选择传感器系数"""
        frame = tk.Frame(parent, bg='#e8e8e8')
        frame.pack(fill=tk.X, pady=5, padx=10)
        
        tk.Label(frame, text="已选择传感器系数:", font=('Arial', 10), bg='#e8e8e8').pack(anchor=tk.W)
        
        # 系数显示区域
        coeff_display = tk.Text(frame, height=3, width=20, font=('Arial', 8),
                               bg='white', state=tk.DISABLED)
        coeff_display.pack(fill=tk.X)

def main():
    """主函数"""
    root = tk.Tk()
    app = TestRightPanel(root)
    root.mainloop()

if __name__ == "__main__":
    main()
