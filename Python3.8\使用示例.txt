X120传感器控制程序 - 使用示例
=====================================

程序启动示例：
--------------
(yolo11) PS F:\激光系统\Python3.8> python x120demo.py

警告：无法加载动态库
尝试过的文件:
  - libx120-dev-sdk.dll
  - libx120-dev-sdk.so

请确保以下文件之一存在于当前目录中:
  - libx120-dev-sdk.dll (Windows版本)
  - libx120-dev-sdk.so (Linux版本)

继续运行程序进行测试（传感器功能将不可用）...
已启用模拟模式
----------X120传感器控制程序----------
----------程序启动成功----------
------------------------------
----------请选择功能----------
------------------------------
1、设置传感器系数
2、查看温补状态
3、设置温补开关
4、计算温度
5、计算压力
6、压力校零
7、温度校N
0、退出
------------------------------

功能测试示例：
--------------

1. 计算温度功能：
输入功能序号：4
----------计算温度----------
输入通道1腔长：123.45
模拟：计算温度，输入腔长=123.45
----------计算温度：25.50°C----------

2. 计算压力功能：
输入功能序号：5
----------计算压力----------
输入通道1腔长：100.0
输入通道2腔长：200.0
模拟：计算压力，输入腔长1=100.0, 腔长2=200.0
----------计算压力：101325.00 Pa----------

3. 查看温补状态：
输入功能序号：2
----------查看温补状态----------
模拟：检查温补状态
----------温补已开启----------

4. 退出程序：
输入功能序号：0
----------退出程序----------

程序特点：
----------
✓ 自动检测并加载动态库
✓ 无动态库时启用模拟模式
✓ 完善的错误处理机制
✓ 用户友好的界面提示
✓ 支持所有传感器功能
✓ 跨平台兼容（Windows/Linux）

注意事项：
----------
1. 模拟模式仅用于程序测试，不与实际硬件交互
2. 实际使用时需要对应的动态库文件
3. 输入数据时请使用数字格式
4. 可以使用 Ctrl+C 随时退出程序
