# X120传感器控制程序 - 增强版使用说明

## 🎉 新功能概览

基于PDF文档中的JR-3220DN通讯协议，我们为您的程序添加了完整的串口通讯功能！

### ✨ 主要改进

1. **完整的串口通讯支持** - 基于RS-232协议
2. **双模式运行** - 串口通讯模式 + 模拟模式
3. **PDF协议完整实现** - 支持所有文档中的命令
4. **实时数据采集** - 腔长数据流采集和保存
5. **智能错误处理** - 完善的异常处理机制

## 📁 文件结构

```
Python3.8/
├── x120demo_enhanced.py     # 增强版主程序
├── jr3220_serial.py         # 串口通讯模块
├── x120demo.py             # 原版程序（保持兼容）
├── README.md               # 原版说明文档
└── 增强版使用说明.md        # 本文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pyserial PyPDF2
```

### 2. 运行程序

```bash
cd Python3.8
python x120demo_enhanced.py
```

### 3. 选择运行模式

程序启动时会提示选择运行模式：

- **选择1**: 串口通讯模式（连接真实硬件）
- **选择2**: 模拟模式（用于测试和演示）

## 🔧 功能详解

### 原有功能（1-7）
- ✅ 设置传感器系数
- ✅ 查看温补状态  
- ✅ 设置温补开关
- ✅ 计算温度
- ✅ 计算压力
- ✅ 压力校零
- ✅ 温度校N

### 新增功能（8-11）
- 🆕 **查询设备版本** - 基于FB03命令
- 🆕 **查询环境压力** - 基于FB57/FB56命令，支持多种单位显示
- 🆕 **查询芯片系数** - 基于F0EE命令，包含CRC校验
- 🆕 **实时腔长数据** - 基于FA070001命令，支持数据保存

## 📡 串口通讯模式详解

### 硬件要求
- JR-3220DN温压解调模块
- RS-232串口连接
- 串口参数：57.6kbps, 8位数据位, 无奇偶校验, 1位停止位

### 支持的命令协议

#### 温压解调模块命令
| 命令 | 功能 | 实现状态 |
|------|------|----------|
| FA070001 | 启用腔长数据传输 | ✅ |
| FA070000 | 停止腔长数据传输 | ✅ |
| FB03 | 查询版本信息 | ✅ |
| FB57/FB56 | 查询环境压力 | ✅ |

#### 芯片连接器命令
| 命令 | 功能 | 实现状态 |
|------|------|----------|
| F0EE | 查询芯片系数 | ✅ |
| EEEEEE | 传感器插入信号检测 | ✅ |

### 数据解析功能

#### 腔长数据解析（8字节格式）
- 帧头：FEFF
- 温度通道：第3-5字节（整数部分+小数部分/256）
- 压力通道：第6-8字节（同温度通道格式）
- 单位：纳米(nm)

#### 环境压力数据解析
- 组合FB57和FB56的响应数据
- 计算公式：压力值 = 组合数据 / 4 (Pa)
- 支持Pa、mmHg、kPa单位显示

#### 芯片系数数据解析
- 帧格式：F1FF + 系数主体 + EEEE + CRC校验码 + FFFF
- ASCII码转换：系数主体转换为可读文本
- CRC-32校验：确保数据完整性

## 🎯 使用示例

### 示例1：查询设备信息
```
输入功能序号：8
----------查询设备版本----------
✓✓✓✓✓✓✓✓✓✓设备版本: 01.02✓✓✓✓✓✓✓✓✓✓
```

### 示例2：查询环境压力
```
输入功能序号：9
----------查询环境压力----------
✓✓✓✓✓✓✓✓✓✓环境压力: 101325.0 Pa✓✓✓✓✓✓✓✓✓✓
----------环境压力: 760.00 mmHg----------
----------环境压力: 101.33 kPa----------
```

### 示例3：实时腔长数据采集
```
输入功能序号：11
----------实时腔长数据----------
输入数据采集时间（秒）[默认10秒]: 5
----------开始采集腔长数据，持续时间: 5.0秒----------
----------按 Ctrl+C 可提前停止采集----------
✓✓✓✓✓✓✓✓✓✓成功采集到 50 组数据✓✓✓✓✓✓✓✓✓✓
----------最近5组数据:----------
14:30:15 - 温度通道: 18222.199nm, 压力通道: 18221.211nm
14:30:16 - 温度通道: 18222.201nm, 压力通道: 18221.209nm
...
是否保存数据到文件? (y/n): y
✓✓✓✓✓✓✓✓✓✓数据已保存到: cavity_data_1703123456.txt✓✓✓✓✓✓✓✓✓✓
```

## 🔍 故障排除

### 常见问题

1. **"ModuleNotFoundError: No module named 'serial'"**
   ```bash
   pip install pyserial
   ```

2. **"未找到可用的串口"**
   - 检查硬件连接
   - 确认设备驱动已安装
   - 检查串口是否被其他程序占用

3. **"串口连接失败"**
   - 检查串口参数设置
   - 确认设备电源状态
   - 尝试不同的串口

4. **"查询命令无响应"**
   - 检查设备是否正常工作
   - 增加命令延迟时间
   - 检查串口通讯线缆

### 调试模式

串口通讯模块支持调试模式，会显示详细的通讯信息：

```python
serial_comm = JR3220Serial(debug=True)
```

## 🔄 版本兼容性

- **向后兼容**: 原版程序`x120demo.py`保持不变
- **功能扩展**: 增强版添加新功能，不影响原有功能
- **模式切换**: 支持动态选择运行模式

## 📊 性能特点

- **实时性**: 支持高频率腔长数据采集
- **稳定性**: 完善的错误处理和重连机制
- **可扩展性**: 模块化设计，易于添加新功能
- **用户友好**: 清晰的界面提示和错误信息

## 🎯 下一步计划

- [ ] 添加数据可视化功能
- [ ] 支持多传感器同时监控
- [ ] 添加数据分析和统计功能
- [ ] 支持网络远程监控
- [ ] 添加配置文件管理

---

**享受您的增强版X120传感器控制程序！** 🎉
