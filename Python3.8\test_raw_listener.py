#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试原始数据监听功能
"""

import serial
import time
import threading

def test_raw_data_listener():
    """测试原始数据监听器"""
    print("=== 测试原始数据监听器 ===")
    
    try:
        # 尝试连接COM3
        ser = serial.Serial(
            port='COM3',
            baudrate=9600,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
        
        print(f"成功连接到 {ser.port}")
        print("开始监听原始数据...")
        print("请在您的其他软件中操作设备...")
        print("-" * 50)
        
        buffer = b''
        packet_count = 0
        
        # 监听30秒
        start_time = time.time()
        while time.time() - start_time < 30:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting)
                if data:
                    packet_count += 1
                    buffer += data
                    
                    print(f"[数据包 {packet_count:03d}] 长度: {len(data)} 字节")
                    print(f"  HEX: {data.hex().upper()}")
                    
                    # 尝试解析ASCII
                    try:
                        ascii_str = data.decode('ascii', errors='ignore')
                        if ascii_str.strip():
                            print(f"  ASCII: '{ascii_str.strip()}'")
                    except:
                        pass
                    
                    # 尝试解析为数值
                    if len(data) >= 4:
                        try:
                            # 尝试不同的数值解析方式
                            import struct
                            if len(data) >= 4:
                                val_int = struct.unpack('>I', data[:4])[0]
                                val_float = struct.unpack('>f', data[:4])[0]
                                print(f"  数值解析: int={val_int}, float={val_float:.3f}")
                        except:
                            pass
                    
                    print("-" * 30)
            
            time.sleep(0.1)
        
        print(f"\n=== 监听完成 ===")
        print(f"总共接收到 {packet_count} 个数据包")
        print(f"总数据长度: {len(buffer)} 字节")
        
        if buffer:
            print(f"完整数据: {buffer.hex().upper()}")
            
            # 分析数据模式
            print("\n=== 数据分析 ===")
            if len(buffer) >= 8:
                print("按8字节分组:")
                for i in range(0, len(buffer), 8):
                    chunk = buffer[i:i+8]
                    print(f"  组 {i//8+1:02d}: {chunk.hex().upper()}")
        else:
            print("未接收到任何数据")
            print("可能的原因:")
            print("1. 设备未连接")
            print("2. 其他软件正在占用串口")
            print("3. 设备处于空闲状态")
        
        ser.close()
        
    except serial.SerialException as e:
        print(f"串口连接失败: {e}")
        print("可能的原因:")
        print("1. COM3端口不存在")
        print("2. 端口被其他程序占用")
        print("3. 权限不足")
    except Exception as e:
        print(f"发生异常: {e}")

if __name__ == "__main__":
    test_raw_data_listener()
