<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X120传感器控制系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .status-card {
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }
        .status-card.connected {
            border-left-color: #28a745;
            background-color: #f8fff9;
        }
        .status-card.disconnected {
            border-left-color: #dc3545;
            background-color: #fff8f8;
        }
        .function-btn {
            margin: 5px;
            min-width: 200px;
        }
        .data-display {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .loading {
            display: none;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .log-container {
            height: 200px;
            overflow-y: auto;
            background-color: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            padding: 10px;
            border-radius: 5px;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cpu"></i> X120传感器控制系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="connectionStatus">
                    <i class="bi bi-circle-fill text-danger"></i> 未连接
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 连接状态卡片 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card status-card disconnected" id="statusCard">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="card-title">
                                    <i class="bi bi-router" id="statusIcon"></i>
                                    连接状态
                                </h5>
                                <p class="card-text" id="statusText">请选择运行模式并连接设备</p>
                                <small class="text-muted" id="deviceInfo">设备信息：未获取</small>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary" id="serialModeBtn">
                                        <i class="bi bi-usb-plug"></i> 串口模式
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" id="simulationModeBtn">
                                        <i class="bi bi-cpu"></i> 模拟模式
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧控制面板 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-gear"></i> 控制面板</h5>
                    </div>
                    <div class="card-body">
                        <!-- 基础功能 -->
                        <h6 class="text-muted">基础功能</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary function-btn" data-function="1">
                                <i class="bi bi-sliders"></i> 设置传感器系数
                            </button>
                            <button class="btn btn-outline-primary function-btn" data-function="2">
                                <i class="bi bi-thermometer"></i> 查看温补状态
                            </button>
                            <button class="btn btn-outline-primary function-btn" data-function="3">
                                <i class="bi bi-toggle-on"></i> 设置温补开关
                            </button>
                            <button class="btn btn-outline-primary function-btn" data-function="4">
                                <i class="bi bi-thermometer-half"></i> 计算温度
                            </button>
                            <button class="btn btn-outline-primary function-btn" data-function="5">
                                <i class="bi bi-speedometer2"></i> 计算压力
                            </button>
                            <button class="btn btn-outline-primary function-btn" data-function="6">
                                <i class="bi bi-arrow-clockwise"></i> 压力校零
                            </button>
                            <button class="btn btn-outline-primary function-btn" data-function="7">
                                <i class="bi bi-arrow-clockwise"></i> 温度校N
                            </button>
                        </div>

                        <hr>

                        <!-- 高级功能 -->
                        <h6 class="text-muted">高级功能</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-success function-btn" data-function="8">
                                <i class="bi bi-info-circle"></i> 查询设备版本
                            </button>
                            <button class="btn btn-outline-success function-btn" data-function="9">
                                <i class="bi bi-cloud"></i> 查询环境压力
                            </button>
                            <button class="btn btn-outline-success function-btn" data-function="10">
                                <i class="bi bi-cpu"></i> 查询芯片系数
                            </button>
                            <button class="btn btn-outline-warning function-btn" data-function="11" id="realTimeBtn">
                                <i class="bi bi-graph-up"></i> 实时腔长数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧数据显示 -->
            <div class="col-md-6">
                <!-- 实时数据显示 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-graph-up"></i> 实时数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="data-display text-center">
                                    <h6>温度通道腔长</h6>
                                    <h4 class="text-primary" id="tempCavity">-- nm</h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="data-display text-center">
                                    <h6>压力通道腔长</h6>
                                    <h4 class="text-success" id="pressCavity">-- nm</h4>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <div class="data-display text-center">
                                    <h6>环境压力</h6>
                                    <h4 class="text-info" id="envPressure">-- Pa</h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="data-display text-center">
                                    <h6>设备版本</h6>
                                    <h4 class="text-warning" id="deviceVersion">--</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表显示 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-bar-chart"></i> 数据图表</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dataChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志显示 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-terminal"></i> 系统日志</h5>
                        <button class="btn btn-sm btn-outline-light" id="clearLogBtn">
                            <i class="bi bi-trash"></i> 清空日志
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-container" id="logContainer">
                            <div>系统启动完成，等待连接...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载动画模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0" id="loadingText">处理中...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
