#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析芯片系数响应数据
根据JR-3220DN文档格式: F1 FF ## ...## EE EE ** ** ** ** FF FF
"""

def parse_coefficient_response(hex_string):
    """解析系数响应数据"""
    try:
        # 清理数据
        hex_clean = hex_string.replace(' ', '').replace('\n', '').replace('\r', '').upper()
        
        print(f"=== 解析芯片系数响应 ===")
        print(f"原始数据长度: {len(hex_clean)} 字符")
        print(f"原始数据: {hex_clean}")
        print()
        
        # 查找系数响应帧 F1FF...FFFF
        start_pattern = "F1FF"
        end_pattern = "FFFF"
        
        start_pos = hex_clean.find(start_pattern)
        if start_pos == -1:
            print("❌ 未找到系数响应帧头 F1FF")
            return None
            
        # 从帧头开始查找
        remaining_data = hex_clean[start_pos:]
        end_pos = remaining_data.find(end_pattern, 4)  # 跳过帧头的FF
        
        if end_pos == -1:
            print("❌ 未找到系数响应帧尾 FFFF")
            return None
            
        # 提取完整帧
        frame = remaining_data[:end_pos + 4]
        print(f"✅ 找到系数响应帧: {frame}")
        print(f"帧长度: {len(frame)} 字符")
        print()
        
        # 查找分隔符 EEEE
        separator_pos = frame.find("EEEE")
        if separator_pos == -1:
            print("❌ 未找到分隔符 EEEE")
            return None
            
        # 提取系数主体 (F1FF之后到EEEE之前)
        coefficient_body = frame[4:separator_pos]  # 跳过F1FF
        print(f"✅ 系数主体: {coefficient_body}")
        print(f"系数主体长度: {len(coefficient_body)} 字符")
        print()
        
        # 转换为ASCII文本
        if len(coefficient_body) % 2 != 0:
            print("❌ 系数主体长度不是偶数")
            return None
            
        try:
            bytes_data = bytes.fromhex(coefficient_body)
            text = bytes_data.decode('ascii', errors='ignore')
            
            print(f"✅ 转换为文本:")
            print(f"原始文本: {repr(text)}")
            print(f"可读文本:")
            print(text)
            print()
            
            # 解析系数
            parse_coefficient_text(text)
            
            return text
            
        except Exception as e:
            print(f"❌ ASCII转换失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return None

def parse_coefficient_text(text):
    """解析系数文本"""
    print("=== 解析系数内容 ===")

    # 处理可能的空字符
    text = text.replace('\x00', '')
    lines = text.strip().split('\n')
    sensor_id = None
    coefficients = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        print(f"处理行: {repr(line)}")

        # 检查是否是传感器ID (通常以FS开头)
        if line.startswith('FS') and not sensor_id:
            sensor_id = line
            print(f"  -> 传感器ID: {sensor_id}")
        else:
            # 尝试解析为数值 (处理科学计数法)
            try:
                # 处理特殊的科学计数法格式 E0+0 -> E+0
                line_fixed = line.replace('E0+', 'E+').replace('E0-', 'E-')
                value = float(line_fixed)
                coefficients.append(value)
                print(f"  -> 系数值: {value}")
            except:
                # 如果不是数值，可能是其他标识符
                if len(line) > 5 and not line.startswith('FS'):
                    print(f"  -> 其他标识: {line}")
                else:
                    print(f"  -> 跳过: {line}")

    print()
    print(f"✅ 解析结果:")
    print(f"传感器ID: {sensor_id}")
    print(f"系数个数: {len(coefficients)}")

    if len(coefficients) >= 9:
        print("\n根据文档格式，系数分配:")
        print("温度系数:")
        print(f"  A0 = {coefficients[0]}")
        print(f"  A1 = {coefficients[1]}")
        print(f"  A2 = {coefficients[2]}")
        print(f"  A3 = {coefficients[3]}")
        print("压力系数:")
        print(f"  B0 = {coefficients[4]}")
        print(f"  B1 = {coefficients[5]}")
        print(f"  B2 = {coefficients[6]}")
        print(f"  B3 = {coefficients[7]}")
        if len(coefficients) > 8:
            print(f"其他系数: {coefficients[8:]}")
    else:
        print("系数:")
        for i, coeff in enumerate(coefficients):
            print(f"  系数 {i+1}: {coeff}")

    return sensor_id, coefficients

if __name__ == "__main__":
    # 测试数据 - 您提供的数据不是标准的系数响应格式
    # 这里用一个模拟的标准格式进行测试
    
    print("=== 测试1: 您提供的数据 ===")
    your_data = "FEFF375610493EA5FEFF37562A493EE0FC030107FEFF375606493EE3FEFF37566D493EF4FEFF37575C493E52FEFF375779493E3BFEFF37579F493E2CFEFF375688493E41FEFF37566F493E74FEFF3756DD493E84FEFF375595493E33FEFF37564B493E25FEFF37557A493DD2FEFF375596493D79FEFF3756DE493DBEFEFF37568E493DB0FEFF375664493E4B"
    
    result = parse_coefficient_response(your_data)
    
    print("\n" + "="*60)
    print("=== 测试2: 模拟标准格式 ===")
    # 模拟一个标准的系数响应 (基于文档中的FS5025示例)
    mock_data = "F1FF46533530323500302E30303030303045302B300A312E33303033323245302B300A332E38323938363545302D340A382E32393739313345302D370A2D312E33373539353745302D380A322E39363239363045302D31310A312E30373833393645302B340A372E34333038373645302D310A2D302E39320A4653323030323430323130303633EEEE12345678FFFF"
    
    result2 = parse_coefficient_response(mock_data)
