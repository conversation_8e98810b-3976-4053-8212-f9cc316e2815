#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试串口数据读取
"""

import time
from jr3220_serial import JR3220Serial

def test_direct_read():
    """直接测试数据读取"""
    print("=== 直接测试串口数据读取 ===")
    
    # 创建串口对象
    serial_comm = JR3220Serial(port="COM3", debug=True)
    
    try:
        # 连接串口
        if not serial_comm.connect():
            print("❌ 串口连接失败")
            return
        
        print("✅ 串口连接成功")
        
        # 启用腔长数据传输
        print("启用腔长数据传输...")
        if serial_comm.enable_cavity_data_transmission():
            print("✅ 腔长数据传输启用成功")
        else:
            print("❌ 腔长数据传输启用失败")
            return
        
        # 等待数据
        print("等待数据...")
        time.sleep(1)
        
        # 连续读取数据
        for i in range(10):
            print(f"\n--- 第 {i+1} 次读取 ---")
            
            # 方法1：读取所有可用数据
            data = serial_comm.read_response(expected_length=None, timeout=1.0)
            if data:
                print(f"读取到 {len(data)} 字节数据: {data.hex()}")
                
                # 查找FEFF帧头
                feff_pos = data.find(bytes.fromhex('FEFF'))
                if feff_pos >= 0:
                    print(f"找到FEFF帧头，位置: {feff_pos}")
                    if len(data) >= feff_pos + 8:
                        frame_data = data[feff_pos:feff_pos + 8]
                        print(f"腔长数据帧: {frame_data.hex()}")
                        
                        # 解析数据
                        parsed = serial_comm.parse_cavity_data(frame_data)
                        if parsed:
                            print(f"解析成功: {parsed}")
                        else:
                            print("解析失败")
                    else:
                        print("数据长度不足8字节")
                else:
                    print("未找到FEFF帧头")
            else:
                print("未读取到数据")
            
            time.sleep(0.5)
    
    except Exception as e:
        print(f"测试异常: {e}")
    
    finally:
        serial_comm.disconnect()
        print("串口已断开")

if __name__ == "__main__":
    test_direct_read()
