// X120传感器控制系统 - 前端JavaScript

class X120Controller {
    constructor() {
        this.isConnected = false;
        this.currentMode = null;
        this.chart = null;
        this.realTimeInterval = null;
        this.chartData = {
            labels: [],
            tempData: [],
            pressData: []
        };
        
        this.init();
    }

    init() {
        this.initChart();
        this.bindEvents();
        this.addLog('系统初始化完成');
    }

    // 初始化图表
    initChart() {
        const ctx = document.getElementById('dataChart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '温度通道腔长 (nm)',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: '压力通道腔长 (nm)',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '腔长 (nm)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '时间'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }

    // 绑定事件
    bindEvents() {
        // 模式选择按钮
        document.getElementById('serialModeBtn').addEventListener('click', () => {
            this.selectMode('serial');
        });

        document.getElementById('simulationModeBtn').addEventListener('click', () => {
            this.selectMode('simulation');
        });

        // 功能按钮
        document.querySelectorAll('.function-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const functionId = e.target.closest('button').dataset.function;
                this.executeFunction(functionId);
            });
        });

        // 清空日志按钮
        document.getElementById('clearLogBtn').addEventListener('click', () => {
            this.clearLog();
        });

        // 实时数据按钮特殊处理
        document.getElementById('realTimeBtn').addEventListener('click', () => {
            this.toggleRealTimeData();
        });
    }

    // 选择运行模式
    async selectMode(mode) {
        this.showLoading('正在连接...');
        
        try {
            const response = await axios.post('/api/connect', { mode: mode });
            
            if (response.data.success) {
                this.currentMode = mode;
                this.isConnected = true;
                this.updateConnectionStatus(true, mode);
                this.addLog(`成功连接到${mode === 'serial' ? '串口' : '模拟'}模式`);
                
                // 获取设备信息
                await this.getDeviceInfo();
            } else {
                this.addLog(`连接失败: ${response.data.message}`, 'error');
            }
        } catch (error) {
            this.addLog(`连接错误: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 执行功能
    async executeFunction(functionId) {
        if (!this.isConnected) {
            this.addLog('请先连接设备', 'warning');
            return;
        }

        const functionNames = {
            '1': '设置传感器系数',
            '2': '查看温补状态',
            '3': '设置温补开关',
            '4': '计算温度',
            '5': '计算压力',
            '6': '压力校零',
            '7': '温度校N',
            '8': '查询设备版本',
            '9': '查询环境压力',
            '10': '查询芯片系数',
            '11': '实时腔长数据'
        };

        this.addLog(`执行功能: ${functionNames[functionId]}`);
        this.showLoading(`执行${functionNames[functionId]}...`);

        try {
            const response = await axios.post('/api/function', { 
                function_id: parseInt(functionId) 
            });

            if (response.data.success) {
                this.handleFunctionResult(functionId, response.data);
            } else {
                this.addLog(`功能执行失败: ${response.data.message}`, 'error');
            }
        } catch (error) {
            this.addLog(`功能执行错误: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    // 处理功能执行结果
    handleFunctionResult(functionId, data) {
        switch (functionId) {
            case '8': // 查询设备版本
                if (data.version) {
                    document.getElementById('deviceVersion').textContent = data.version;
                    this.addLog(`设备版本: ${data.version}`, 'success');
                }
                break;
                
            case '9': // 查询环境压力
                if (data.pressure !== undefined) {
                    document.getElementById('envPressure').textContent = `${data.pressure.toFixed(1)} Pa`;
                    this.addLog(`环境压力: ${data.pressure.toFixed(1)} Pa`, 'success');
                }
                break;
                
            case '10': // 查询芯片系数
                if (data.coefficient) {
                    this.addLog(`芯片系数: ${data.coefficient.coefficient_text}`, 'success');
                    if (data.coefficient.crc_valid) {
                        this.addLog('CRC校验通过', 'success');
                    } else {
                        this.addLog('CRC校验失败', 'warning');
                    }
                }
                break;
                
            default:
                this.addLog(`功能执行完成: ${data.message || '成功'}`, 'success');
        }
    }

    // 切换实时数据采集
    async toggleRealTimeData() {
        const btn = document.getElementById('realTimeBtn');
        
        if (this.realTimeInterval) {
            // 停止实时数据采集
            clearInterval(this.realTimeInterval);
            this.realTimeInterval = null;
            btn.innerHTML = '<i class="bi bi-graph-up"></i> 实时腔长数据';
            btn.classList.remove('btn-outline-danger');
            btn.classList.add('btn-outline-warning');
            this.addLog('停止实时数据采集');
        } else {
            // 开始实时数据采集
            btn.innerHTML = '<i class="bi bi-stop-circle"></i> 停止采集';
            btn.classList.remove('btn-outline-warning');
            btn.classList.add('btn-outline-danger');
            this.addLog('开始实时数据采集');
            
            this.realTimeInterval = setInterval(() => {
                this.fetchRealTimeData();
            }, 1000); // 每秒更新一次
        }
    }

    // 获取实时数据
    async fetchRealTimeData() {
        try {
            const response = await axios.get('/api/realtime-data');
            
            if (response.data.success && response.data.data) {
                const data = response.data.data;
                
                // 更新显示
                document.getElementById('tempCavity').textContent = `${data.temperature_cavity.toFixed(3)} nm`;
                document.getElementById('pressCavity').textContent = `${data.pressure_cavity.toFixed(3)} nm`;
                
                // 更新图表
                this.updateChart(data);
            }
        } catch (error) {
            console.error('获取实时数据失败:', error);
        }
    }

    // 更新图表
    updateChart(data) {
        const now = new Date().toLocaleTimeString();
        
        // 保持最多50个数据点
        if (this.chartData.labels.length >= 50) {
            this.chartData.labels.shift();
            this.chartData.tempData.shift();
            this.chartData.pressData.shift();
        }
        
        this.chartData.labels.push(now);
        this.chartData.tempData.push(data.temperature_cavity);
        this.chartData.pressData.push(data.pressure_cavity);
        
        this.chart.data.labels = this.chartData.labels;
        this.chart.data.datasets[0].data = this.chartData.tempData;
        this.chart.data.datasets[1].data = this.chartData.pressData;
        
        this.chart.update('none'); // 无动画更新，提高性能
    }

    // 获取设备信息
    async getDeviceInfo() {
        try {
            const response = await axios.get('/api/device-info');
            
            if (response.data.success) {
                const info = response.data.info;
                
                if (info.version) {
                    document.getElementById('deviceVersion').textContent = info.version;
                }
                
                if (info.environmental_pressure !== null) {
                    document.getElementById('envPressure').textContent = `${info.environmental_pressure.toFixed(1)} Pa`;
                }
                
                this.addLog('设备信息获取完成', 'success');
            }
        } catch (error) {
            console.error('获取设备信息失败:', error);
        }
    }

    // 更新连接状态
    updateConnectionStatus(connected, mode = null) {
        const statusCard = document.getElementById('statusCard');
        const statusIcon = document.getElementById('statusIcon');
        const statusText = document.getElementById('statusText');
        const connectionStatus = document.getElementById('connectionStatus');
        
        if (connected) {
            statusCard.classList.remove('disconnected');
            statusCard.classList.add('connected');
            statusIcon.className = 'bi bi-check-circle-fill text-success';
            statusText.textContent = `已连接到${mode === 'serial' ? '串口' : '模拟'}模式`;
            connectionStatus.innerHTML = '<i class="bi bi-circle-fill text-success"></i> 已连接';
        } else {
            statusCard.classList.remove('connected');
            statusCard.classList.add('disconnected');
            statusIcon.className = 'bi bi-x-circle-fill text-danger';
            statusText.textContent = '连接已断开';
            connectionStatus.innerHTML = '<i class="bi bi-circle-fill text-danger"></i> 未连接';
        }
    }

    // 显示加载动画
    showLoading(text = '处理中...') {
        document.getElementById('loadingText').textContent = text;
        const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
        modal.show();
    }

    // 隐藏加载动画
    hideLoading() {
        const modal = bootstrap.Modal.getInstance(document.getElementById('loadingModal'));
        if (modal) {
            modal.hide();
        }
    }

    // 添加日志
    addLog(message, type = 'info') {
        const logContainer = document.getElementById('logContainer');
        const timestamp = new Date().toLocaleTimeString();
        
        let color = '#00ff00'; // 默认绿色
        switch (type) {
            case 'error':
                color = '#ff4444';
                break;
            case 'warning':
                color = '#ffaa00';
                break;
            case 'success':
                color = '#44ff44';
                break;
        }
        
        const logEntry = document.createElement('div');
        logEntry.style.color = color;
        logEntry.textContent = `[${timestamp}] ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // 清空日志
    clearLog() {
        const logContainer = document.getElementById('logContainer');
        logContainer.innerHTML = '<div>日志已清空</div>';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.x120Controller = new X120Controller();
});
