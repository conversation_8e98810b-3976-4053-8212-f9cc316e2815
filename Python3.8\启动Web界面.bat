@echo off
chcp 65001 >nul
echo ========================================
echo    X120传感器控制系统 - Web界面启动器
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请确保已安装Python并添加到系统PATH中
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import flask, flask_cors, pyserial" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install flask flask-cors pyserial PyPDF2
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo 依赖检查完成！
echo.
echo 启动Web服务器...
echo 请在浏览器中访问: http://localhost:5000
echo 按 Ctrl+C 可停止服务器
echo.

python web_app.py

echo.
echo Web服务器已停止
pause
