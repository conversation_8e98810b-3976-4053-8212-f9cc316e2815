@echo off
chcp 65001 >nul
echo ========================================
echo  白光干涉高温高压软件TP v2.6.8 - 启动器
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请确保已安装Python并添加到系统PATH中
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import tkinter, matplotlib, pyserial" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install matplotlib pyserial PyPDF2
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo 依赖检查完成！
echo.
echo 启动白光干涉高温高压软件...
echo.

python x120_gui_v2.py

echo.
echo 程序已关闭
pause
