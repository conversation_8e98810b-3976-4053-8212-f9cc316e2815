#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JR-3220DN 串口通讯测试脚本
用于测试串口通讯模块的各项功能
"""

import sys
import time
from jr3220_serial import JR3220Serial

def test_serial_connection():
    """测试串口连接"""
    print("=" * 60)
    print("测试1: 串口连接功能")
    print("=" * 60)
    
    try:
        with JR3220Serial(debug=True) as comm:
            # 列出可用串口
            ports = comm.list_available_ports()
            print(f"发现 {len(ports)} 个可用串口")
            
            if not ports:
                print("⚠️  未找到可用串口，跳过连接测试")
                return False
            
            # 尝试连接第一个串口
            if comm.connect(ports[0]):
                print("✅ 串口连接测试通过")
                return True
            else:
                print("❌ 串口连接测试失败")
                return False
                
    except Exception as e:
        print(f"❌ 串口连接测试异常: {e}")
        return False

def test_command_sending():
    """测试命令发送功能"""
    print("\n" + "=" * 60)
    print("测试2: 命令发送功能")
    print("=" * 60)
    
    try:
        with JR3220Serial(debug=True) as comm:
            ports = comm.list_available_ports()
            if not ports:
                print("⚠️  未找到可用串口，跳过命令发送测试")
                return False
            
            if not comm.connect(ports[0]):
                print("❌ 无法连接串口")
                return False
            
            # 测试发送版本查询命令
            print("发送版本查询命令...")
            if comm.send_command(comm.CMD_VERSION_QUERY):
                print("✅ 命令发送成功")
                
                # 尝试读取响应
                response = comm.read_response(timeout=2.0)
                if response:
                    print(f"✅ 收到响应: {response.hex().upper()}")
                else:
                    print("⚠️  未收到响应（可能设备未连接）")
                return True
            else:
                print("❌ 命令发送失败")
                return False
                
    except Exception as e:
        print(f"❌ 命令发送测试异常: {e}")
        return False

def test_data_parsing():
    """测试数据解析功能"""
    print("\n" + "=" * 60)
    print("测试3: 数据解析功能")
    print("=" * 60)
    
    try:
        comm = JR3220Serial(debug=True)
        
        # 测试腔长数据解析
        print("测试腔长数据解析...")
        test_cavity_data = bytes.fromhex('FEFF472E33472D36')  # 来自PDF文档的示例
        parsed_cavity = comm.parse_cavity_data(test_cavity_data)
        
        if parsed_cavity:
            print("✅ 腔长数据解析成功:")
            print(f"   温度通道: {parsed_cavity['temperature_cavity']:.3f} nm")
            print(f"   压力通道: {parsed_cavity['pressure_cavity']:.3f} nm")
            
            # 验证解析结果（基于PDF文档示例）
            expected_temp = 18222.199  # 0x472E + 0x33/256
            expected_press = 18221.211  # 0x472D + 0x36/256
            
            if abs(parsed_cavity['temperature_cavity'] - expected_temp) < 0.01:
                print("✅ 温度通道解析正确")
            else:
                print(f"❌ 温度通道解析错误，期望: {expected_temp}")
            
            if abs(parsed_cavity['pressure_cavity'] - expected_press) < 0.01:
                print("✅ 压力通道解析正确")
            else:
                print(f"❌ 压力通道解析错误，期望: {expected_press}")
        else:
            print("❌ 腔长数据解析失败")
            return False
        
        # 测试压力数据解析
        print("\n测试环境压力数据解析...")
        test_pressure_data1 = bytes.fromhex('FC570006')
        test_pressure_data2 = bytes.fromhex('FC56231A')
        parsed_pressure = comm.parse_pressure_data(test_pressure_data1, test_pressure_data2)
        
        if parsed_pressure:
            print(f"✅ 环境压力解析成功: {parsed_pressure:.1f} Pa")
            
            # 验证解析结果（基于PDF文档示例）
            expected_pressure = 100550.5  # 0x6231A / 4
            if abs(parsed_pressure - expected_pressure) < 1.0:
                print("✅ 环境压力解析正确")
            else:
                print(f"❌ 环境压力解析错误，期望: {expected_pressure}")
        else:
            print("❌ 环境压力解析失败")
            return False
        
        # 测试芯片系数解析（模拟数据）
        print("\n测试芯片系数数据解析...")
        # 构造一个简单的芯片系数数据包
        coeff_text = "TEST_COEFFICIENT_123"
        coeff_bytes = coeff_text.encode('ascii')
        
        # 计算CRC
        import zlib
        crc = zlib.crc32(coeff_bytes) & 0xffffffff
        
        # 构造完整数据包
        test_chip_data = (bytes.fromhex('F1FF') + 
                         coeff_bytes + 
                         bytes.fromhex('EEEE') + 
                         crc.to_bytes(4, 'big') + 
                         bytes.fromhex('FFFF'))
        
        parsed_chip = comm.parse_chip_coefficient(test_chip_data)
        
        if parsed_chip and parsed_chip.get('status') == 'success':
            print("✅ 芯片系数解析成功:")
            print(f"   系数文本: {parsed_chip['coefficient_text']}")
            print(f"   CRC校验: {'通过' if parsed_chip['crc_valid'] else '失败'}")
            
            if parsed_chip['coefficient_text'] == coeff_text:
                print("✅ 系数文本解析正确")
            else:
                print("❌ 系数文本解析错误")
            
            if parsed_chip['crc_valid']:
                print("✅ CRC校验正确")
            else:
                print("❌ CRC校验错误")
        else:
            print("❌ 芯片系数解析失败")
            return False
        
        print("✅ 所有数据解析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据解析测试异常: {e}")
        return False

def test_high_level_functions():
    """测试高级功能"""
    print("\n" + "=" * 60)
    print("测试4: 高级功能测试")
    print("=" * 60)
    
    try:
        with JR3220Serial(debug=True) as comm:
            ports = comm.list_available_ports()
            if not ports:
                print("⚠️  未找到可用串口，跳过高级功能测试")
                return False
            
            if not comm.connect(ports[0]):
                print("❌ 无法连接串口")
                return False
            
            # 测试设备信息获取
            print("获取设备完整信息...")
            device_info = comm.get_device_info()
            
            print("✅ 设备信息获取完成:")
            print(f"   连接状态: {device_info['connected']}")
            print(f"   串口: {device_info['port']}")
            print(f"   版本: {device_info['version'] or '未获取到'}")
            print(f"   环境压力: {device_info['environmental_pressure'] or '未获取到'}")
            print(f"   芯片系数: {device_info['chip_coefficient'] or '未获取到'}")
            
            return True
            
    except Exception as e:
        print(f"❌ 高级功能测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("JR-3220DN 串口通讯模块测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("串口连接", test_serial_connection()))
    test_results.append(("命令发送", test_command_sending()))
    test_results.append(("数据解析", test_data_parsing()))
    test_results.append(("高级功能", test_high_level_functions()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！串口通讯模块工作正常。")
    else:
        print("⚠️  部分测试失败，请检查硬件连接或代码实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
