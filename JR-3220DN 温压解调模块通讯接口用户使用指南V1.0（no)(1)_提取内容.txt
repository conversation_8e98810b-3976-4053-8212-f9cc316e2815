第1页:
JR等相关产品标识所有，不得随意侵犯
文档编号：JR-3220DNV1.0发布版本/202407©Copyright2024
本手册包含的产品信息和应用说明可能会随时变动，恕不另行通知，请确保
您的文档是最新版本。除非版权法允许，否则在未经事先书面许可的情况
下，严禁复制、改编、转载或翻译本手册的内容本指导手册仅适用于温压解调模块，支持产品的型号JR-3220DNJR-3220DN
温压解调模块
通讯接口协议
用户指导手册


第2页:
文档编号：JR-3220DNV1.0发布版本/202407/页1产品安全信息与阅读指南
△!警告安全事项
在使用本产品时，一定要遵守基本的安全注意事项以降低火灾或触电的可能性
请勿将本产品直接放在水中或任何高度潮湿环境中安装或使用
△!警告触电隐患
使用之前，请仔细阅读并熟知《产品安装手册》中的所有说明并留意产品上标
出的所有警告和说明
仪器应在通风状况好的地方使用
产品使用前推荐安放在稳固的机械平台或机柜，确保安装在安全的地方
产品使用时避免踩到或绊到电源线，造成电源线或解调仪受损坏
为设备连接外部电源时，只使用接地的电源插座。如果不知道插座是否有地线，
请先咨询电气技术人员
必须使用原厂提供的电源适配器
必须按照原厂要求的串口通讯定义方式连接外部线缆
进行清洁前，必须先拔掉产品的电源插头
产品开机以后，建议进行完5分钟的预热运行后再做相关的操作
如果本产品无法正常工作，请参考《产品安装手册》寻求帮助
△!警告本说明书按“现状”提供，编排时力求内容全面正确可靠，已
经尽最大的努力避免任何的人为失误，但实际上还有如文件格式转换、分
发、传输、印刷等无法控制环节，因而不保证用户收到之时没有任何错误。
对于文档中所有明示或暗示的陈述、保证和条款，均不提供任何法律保证；
且不对任何因使用本文档的任何内容所导致的任何损失承担任何或连带的
法律责任，除非此类免责声明在法律上被视为无效。

第3页:
文档编号：JR-3220DNV1.0发布版本/202407/页2目录
目录.........................................................................................................................................................2
第一章RS-232通讯控制硬件接口.....................................................................................................3
第二章串口通讯命令行........................................................................................................................5
2.1串口通讯输入..............................................................................................................................5
2.2串口通讯延迟..............................................................................................................................5
2.3串口通讯命令行..........................................................................................................................5
2.3.1命令行总结...........................................................................................................................5
2.3.2配置类...................................................................................................................................5
2.3.3数据类...................................................................................................................................6
产品支持和咨询服务.............................................................................................................................8

第4页:
文档编号：JR-3220DNV1.0发布版本/202407/页3第一章RS-232通讯控制硬件接口
JR-3220DN温压解调模块通过RS-232接口与外部主机完成通讯连接。
RS-232接口如下图1.1所示3针接口，其外部匹配的连接器可选用
Molex的50579703或50579403。
图1.1

第5页:
文档编号：JR-3220DNV1.0发布版本/202407/页4模块RS-232接口配置参数的定义如下：
波特率 57.6kbps
数据位数 8位
奇偶校验 无
停止位 1位
流控（握手协议） 无
表1.1RS-232接口配置
解调模块的RS-232命令字目前不支持国际标准化组织推荐的可编程
仪器标准化指令集（SCPI,standardcommandsforprogrammable
instrumentation）的格式和语法，采用自定义的形式。具体的命令定
义参见第二章。
面板串行RS232的定义如下：
管脚1 RS232输出
管脚2 通讯接口地，需要与
电源地同源
管脚3 RS232输入
表1.2RS-232接口定义
产品后续更新中串口命令定义可能会与当前的不保持一致。如有修改，恕不通知。

第6页:
文档编号：JR-3220DNV1.0发布版本/202407/页5第二章串口通讯命令行
2.1串口通讯输入
模块通讯串口命令行采用16进制格式（非ASCII码），不强制要求
采用回车键(Enter)作为命令行终结符。
2.2串口通讯延迟
串口通讯在处理命令行时，通常会有时间延迟等待和迟滞响应的现
象，因而外部接口程序发送命令后需要添加适当的延迟时间余量，以便于
解调仪能正确地响应程序发送的指令并回送数据。
2.3串口通讯命令行
2.3.1命令行总结
类别 命令行 说明
配置类FA070001 使模块对外发送腔长数据
FA070000 停止模块对外发送腔长数据
数据类FB03 查询模块当前代码版本信息
FEFF****##****##模块上传外部传感器温度通道和压力
通道腔长数据，为8字节格式
FB57/FB56 模块发送环境压力
表2.1串口命令字总结
2.3.2配置类
a.使能模块发送腔长数据（FA070001）
模块上电缺省以后不对外发送腔长信息，外部控制器准备好以后，需通过此命
令使能模块对外发送传感器的腔长信息。命令一旦发送成功，模块会立即发送传感器
腔长信息。

第7页:
文档编号：JR-3220DNV1.0发布版本/202407/页6b.停止模块发送腔长数据（FA070000）
模块被外部控制器成功使能后，对外发送传感器的腔长信息。外部控制器可通
过此命令则要求模块停止对外发送传感器的腔长信息。
2.3.3数据类
a.模块版本信息查询（FB03）
本命令用来查询模块当前内部固件版本信息。命令发送成功后，模块会上传模块当
前内部固件的版本信息。返回的信息格式为FC03**##，其中**，##分别代表1字节
的16进制数据。如下图2.1参考示例，返回当前版本为0001。
图2.1
b.模块发送腔长数据（FEFF****##****##）
模块采用总计8字节数据包格式上传腔长数据：
数据包第1和第2字节固定为FEFF；
第3至第5字节****##为温度通道传感器腔长信息，单位为nm，3字节
中前两字节（第3和第4字节）****表示整数部分，最后一个字节（第5
字节）##表示小数部分。整数部分直接转换为10进制即可，小数则需要
先转换成10进制，再除以256即为实际所需要的数据，转换后的整数和
小数部分相加即可得到单次实际测量结果。
第6至第8字节****##为压力通道传感器腔长信息，单位为nm，3字节
的数据表达和计算方式与温度通道的3个字节一致，即第6和第7字节为
整部分，第8个字节为小数部分。
例：
如下图2.2第一行所示上报的腔长信息：
16进制整数0x472e（第3、4字节），转换为10进制18222。
小数部分16进制0x33（第5字节）数据转换为10进制51，再除以256，
按算式51/256=0.199nm
相加整数和小数腔长，即可计算得到温度通道的腔长为18222.199nm
16进制整数0x472d（第6、7字节），转换为10进制18221。

第8页:
文档编号：JR-3220DNV1.0发布版本/202407/页7小数部分16进制0x36（第8字节）数据转换为10进制54，再除以256，
按算式54/256=0.211nm
相加整数和小数腔长，即可计算得到压力通道的腔长为18221.211nm
图2.2
当外部传感器不存在时或传感器状态不对时，模块会上传FEFFFFFFFFFFFFFF
数据包来表示无法检测到当前传感器的腔长。
c.模块发送环境压力
发送命令FB57返回FC57####*
发送命令FB56返回FC56####
0x*####共20位用来表示当前环境绝对压力，单位帕；其中前18位为整数，最
后2位为小数。为了便于计算，可以将0x*####转换为十进制并除以4，就是当前的环
境大气压数值(A)
常用压力单位转换参考：
1Pa=7.5×10^-3mmHg
例：
发送命令FB57，返回FC570006；发送命令FB56返回FC56231A。
0x6231A转为十进制为402202，除以4就是当前的大气压数值100550.5Pa(A)

第9页:
文档编号：JR-3220DNV1.0发布版本/202407/页8产品支持和咨询服务
如果您在使用过程中发现有任何错误或有什么问题，请联系咨询我们。