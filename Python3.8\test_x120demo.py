#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
X120 Demo 测试脚本
用于测试 x120demo.py 的基本功能
"""

import sys
import os

def test_import():
    """测试是否能正常导入模块"""
    try:
        # 添加当前目录到 Python 路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 尝试导入模块（但不执行主程序）
        import importlib.util
        spec = importlib.util.spec_from_file_location("x120demo", "x120demo.py")
        x120demo = importlib.util.module_from_spec(spec)
        
        print("✓ 模块导入测试通过")
        print("✓ 语法检查通过")
        
        # 检查主要函数是否存在
        functions_to_check = [
            'info_print',
            'print_error', 
            'print_hint',
            'call_set_sensor_coefficients',
            'call_is_temperature_compensation_enabled',
            'call_enable_temperature_compensation',
            'call_compute_temperature',
            'call_compute_pressure',
            'call_calibrate_pressure',
            'call_calibrate_temperature',
            'main'
        ]
        
        spec.loader.exec_module(x120demo)
        
        missing_functions = []
        for func_name in functions_to_check:
            if not hasattr(x120demo, func_name):
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"✗ 缺少函数: {', '.join(missing_functions)}")
            return False
        else:
            print("✓ 所有必需函数都存在")
        
        # 检查 functions 列表是否正确
        if hasattr(x120demo, 'functions') and len(x120demo.functions) == 8:
            print("✓ functions 列表定义正确")
        else:
            print("✗ functions 列表定义有问题")
            return False
            
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False
    except SyntaxError as e:
        print(f"✗ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_library_loading():
    """测试动态库加载逻辑"""
    print("\n测试动态库加载逻辑:")
    
    # 检查当前目录下是否有动态库文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    if os.name == 'nt':  # Windows
        lib_file = os.path.join(current_dir, "libx120-dev-sdk.dll")
        expected_ext = ".dll"
    else:  # Linux/Unix
        lib_file = os.path.join(current_dir, "libx120-dev-sdk.so")
        expected_ext = ".so"
    
    if os.path.exists(lib_file):
        print(f"✓ 找到动态库文件: {lib_file}")
    else:
        print(f"⚠ 未找到动态库文件: {lib_file}")
        print(f"  程序运行时会显示错误信息并退出，这是正常的")
        print(f"  请确保将对应的动态库文件({expected_ext})放在程序目录中")

def main():
    """主测试函数"""
    print("X120 Demo 程序测试")
    print("=" * 40)
    
    # 测试模块导入
    if test_import():
        print("✓ 基本功能测试通过")
    else:
        print("✗ 基本功能测试失败")
        return False
    
    # 测试动态库加载逻辑
    test_library_loading()
    
    print("\n" + "=" * 40)
    print("测试完成!")
    print("\n使用说明:")
    print("1. 将对应的动态库文件放在程序目录中:")
    print("   - Windows: libx120-dev-sdk.dll")
    print("   - Linux/Unix: libx120-dev-sdk.so")
    print("2. 运行: python x120demo.py")
    print("3. 按照菜单提示操作")
    
    return True

if __name__ == "__main__":
    main()
