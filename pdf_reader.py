#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文件内容提取工具
"""

import PyPDF2
import os
import sys

def extract_pdf_text(pdf_path):
    """提取PDF文件的文本内容"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = []
            
            print(f"PDF文件: {os.path.basename(pdf_path)}")
            print(f"总页数: {len(pdf_reader.pages)}")
            print("=" * 60)
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        print(f"\n--- 第 {page_num} 页 ---")
                        print(page_text)
                        text_content.append(f"第{page_num}页:\n{page_text}")
                except Exception as e:
                    print(f"提取第{page_num}页时出错: {e}")
                    
            return "\n\n".join(text_content)
            
    except Exception as e:
        print(f"读取PDF文件失败: {e}")
        return None

def main():
    """主函数"""
    # PDF文件列表
    pdf_files = [
        "JR-3220DN 温压解调模块通讯接口用户使用指南V1.0（no)(1).pdf",
        "JR-3220DN系列-芯片连接器-通讯指南-0.9.9(1).pdf"
    ]
    
    for pdf_file in pdf_files:
        if os.path.exists(pdf_file):
            print(f"\n{'='*80}")
            print(f"正在读取: {pdf_file}")
            print(f"{'='*80}")
            
            text = extract_pdf_text(pdf_file)
            if text:
                # 保存提取的文本到文件
                output_file = f"{os.path.splitext(pdf_file)[0]}_提取内容.txt"
                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(text)
                    print(f"\n内容已保存到: {output_file}")
                except Exception as e:
                    print(f"保存文件失败: {e}")
            else:
                print("未能提取到内容")
        else:
            print(f"文件不存在: {pdf_file}")

if __name__ == "__main__":
    main()
