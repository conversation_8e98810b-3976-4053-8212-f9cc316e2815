#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试芯片系数查询
"""

import time
from jr3220_serial import JR3220Serial

def test_coefficient_query():
    """测试芯片系数查询"""
    print("=== 测试芯片系数查询 ===")
    
    # 创建串口连接
    serial_comm = JR3220Serial(debug=True)
    
    # 连接串口
    port = "COM3"  # 根据实际情况修改
    if not serial_comm.connect(port):
        print(f"❌ 无法连接到串口 {port}")
        return
    
    print(f"✅ 成功连接到串口 {port}")
    
    try:
        print("\n=== 发送芯片系数查询命令 F0 EE ===")
        
        # 发送查询命令
        result = serial_comm.query_chip_coefficient()
        
        if result:
            print(f"✅ 查询成功!")
            print(f"状态: {result.get('status')}")
            
            if result.get('status') == 'success':
                print(f"系数文本: {result.get('coefficient_text')}")
                print(f"CRC校验: {'通过' if result.get('crc_valid') else '失败'}")
                
                # 解析系数
                coeff_text = result.get('coefficient_text', '')
                parse_coefficient_text(coeff_text)
                
            elif result.get('status') == 'no_chip':
                print("未检测到系数芯片")
        else:
            print("❌ 查询失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        serial_comm.disconnect()
        print("串口已断开")

def parse_coefficient_text(text):
    """解析系数文本"""
    print("\n=== 解析系数文本 ===")
    
    if not text:
        print("系数文本为空")
        return
    
    lines = text.strip().split('\n')
    sensor_id = None
    coefficients = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        print(f"处理行: {repr(line)}")
        
        # 检查是否是传感器ID (通常以FS开头)
        if line.startswith('FS') and not sensor_id:
            sensor_id = line
            print(f"  -> 传感器ID: {sensor_id}")
        else:
            # 尝试解析为数值 (处理科学计数法)
            try:
                # 处理特殊的科学计数法格式 E0+0 -> E+0
                line_fixed = line.replace('E0+', 'E+').replace('E0-', 'E-')
                value = float(line_fixed)
                coefficients.append(value)
                print(f"  -> 系数值: {value}")
            except:
                # 如果不是数值，可能是其他标识符
                if len(line) > 5 and not line.startswith('FS'):
                    print(f"  -> 其他标识: {line}")
                else:
                    print(f"  -> 跳过: {line}")
    
    print(f"\n✅ 解析结果:")
    print(f"传感器ID: {sensor_id}")
    print(f"系数个数: {len(coefficients)}")
    
    if len(coefficients) >= 8:
        print("\n根据文档格式，系数分配:")
        print("温度系数:")
        print(f"  A0 = {coefficients[0]}")
        print(f"  A1 = {coefficients[1]}")
        print(f"  A2 = {coefficients[2]}")
        print(f"  A3 = {coefficients[3]}")
        print("压力系数:")
        print(f"  B0 = {coefficients[4]}")
        print(f"  B1 = {coefficients[5]}")
        print(f"  B2 = {coefficients[6]}")
        print(f"  B3 = {coefficients[7]}")
        if len(coefficients) > 8:
            print(f"其他系数: {coefficients[8:]}")
    else:
        print("系数:")
        for i, coeff in enumerate(coefficients):
            print(f"  系数 {i+1}: {coeff}")

if __name__ == "__main__":
    test_coefficient_query()
