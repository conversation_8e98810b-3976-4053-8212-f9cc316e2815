# 白光干涉高温高压软件 - 真实数据接收使用说明

## 🎯 功能概述

新版GUI程序现在支持接收真实的传感器数据，不再只是模拟数据！程序可以：

1. **通过串口接收真实腔长数据**
2. **自动查询和加载芯片系数**
3. **将腔长数据转换为温度和压力值**
4. **支持手动加载系数文件**

## 📋 使用步骤

### 1. 连接硬件设备
- 将JR-3220DN温压解调模块通过串口连接到电脑
- 确保串口参数正确：57600波特率，8数据位，无奇偶校验，1停止位

### 2. 选择串口
1. 在右侧参数配置面板中找到"串口"下拉菜单
2. 选择对应的COM端口（如COM3）
3. 程序会自动尝试连接串口
4. 连接成功后，状态指示灯变为绿色

### 3. 系数管理

#### 自动加载芯片系数（推荐）
- 连接串口后，程序会自动查询芯片中的系数
- 如果检测到系数芯片，会自动加载系数
- 系数信息会显示在右侧面板底部的系数显示区域

#### 手动加载系数文件
1. 点击"点击选择"按钮
2. 选择系数文件（支持JSON格式或文本格式）
3. 程序会尝试解析并加载系数

### 4. 开始接收数据
1. 点击"开始接收数据"按钮
2. 程序开始接收真实的腔长数据
3. 根据加载的系数自动计算温度和压力值
4. 实时显示在图表和数值框中

## 📁 系数文件格式

### JSON格式（推荐）
```json
{
    "temperature": {
        "A0": 25.0,
        "A1": 0.001234,
        "A2": -0.0000056,
        "A3": 0.000000012,
        "B0": 0.5,
        "B1": 0.0001,
        "B2": 0.0,
        "B3": 0.0
    },
    "pressure": {
        "C0": 101325.0,
        "C1": 0.0567,
        "C2": -0.000123,
        "C3": 0.0000000789,
        "D0": -12.34,
        "D1": 0.456,
        "D2": 0.0,
        "D3": 0.0
    },
    "reference_temperature": 25.0,
    "reference_pressure": 101325.0
}
```

### 文本格式（芯片数据格式）
```
A0=25.0
A1=0.001234
A2=-0.0000056
A3=0.000000012
B0=0.5
B1=0.0001
C0=101325.0
C1=0.0567
C2=-0.000123
C3=0.0000000789
D0=-12.34
D1=0.456
```

## 🧮 计算公式

### 温度计算
```
T = A0 + A1*CL + A2*CL² + A3*CL³ + B0*exp(B1*CL)
```
其中：
- T：温度（℃）
- CL：温度通道腔长（nm）
- A0-A3, B0-B1：温度系数

### 压力计算
```
P = C0 + C1*CL2 + C2*CL2² + C3*CL2³ + D0*T + D1*T²
```
其中：
- P：压力（Pa）
- CL2：压力通道腔长（nm）
- T：温度（℃）
- C0-C3, D0-D1：压力系数

## 🔧 故障排除

### 串口连接失败
1. 检查串口是否被其他程序占用
2. 确认串口号是否正确
3. 检查硬件连接是否正常
4. 尝试重新插拔USB转串口设备

### 无法接收数据
1. 确认串口连接成功（绿色指示灯）
2. 检查传感器是否正常工作
3. 查看控制台输出的调试信息

### 系数加载失败
1. 检查系数文件格式是否正确
2. 确认文件编码为UTF-8
3. 验证系数数值是否合理

### 计算结果异常
1. 检查系数是否正确加载
2. 确认腔长数据是否正常
3. 验证系数的有效范围

## 📊 数据显示说明

### 实时数值显示
- 顶部蓝色数值框显示当前温度和压力值
- 数值每0.5秒更新一次

### 图表显示
- 蓝色曲线：温度变化
- 绿色曲线：压力变化
- 红色圆点：当前数据点
- 白色信息框：实时状态信息

### 状态指示
- 绿色圆点：串口连接正常
- 红色圆点：串口未连接或连接失败

## 🎛️ 高级功能

### 温度补偿
- 右侧面板中的粉色开关可以启用/禁用温度补偿
- 温度补偿会影响压力计算的精度

### 校零功能
- 温度校零：在已知温度环境中校准温度零点
- 压力校零：在已知压力环境中校准压力零点

### 数据保存
- 支持CSV、Excel、TXT格式保存
- 可以保存实时数据用于后续分析

## 📝 注意事项

1. **系数重要性**：没有正确的系数，无法得到准确的温度压力值
2. **芯片优先**：程序优先使用芯片中的系数，其次是手动加载的文件
3. **单位转换**：程序内部使用Pa为压力单位，显示时转换为mmHg
4. **数据精度**：计算精度取决于系数的准确性和腔长测量精度
5. **环境影响**：温度和压力测量会受到环境条件影响

## 🔄 从模拟数据切换到真实数据

1. **模拟模式**：串口未连接或连接失败时自动使用模拟数据
2. **真实模式**：串口连接成功后自动切换到真实数据
3. **无缝切换**：程序会自动检测并切换数据源，无需重启

---

**提示**：首次使用建议先用提供的`example_coefficients.json`文件测试系数加载功能，确保程序工作正常后再连接真实硬件。
