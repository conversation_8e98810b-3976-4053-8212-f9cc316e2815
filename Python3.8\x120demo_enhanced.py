#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
X120传感器控制程序 - 增强版
集成了完整的串口通讯功能和PDF文档中的所有协议
"""

import os
import sys
import time
from ctypes import *
from jr3220_serial import JR3220Serial

# 全局变量
libx120 = None
serial_comm = None
use_serial_mode = False

# 尝试加载动态库，处理不同操作系统的情况
library_loaded = False
library_files = []

if os.name == 'nt':  # Windows
    library_files = ["libx120-dev-sdk.dll", "libx120-dev-sdk.so"]
else:  # Linux/Unix
    library_files = ["./libx120-dev-sdk.so", "./libx120-dev-sdk.dll"]

for lib_file in library_files:
    try:
        libx120 = cdll.LoadLibrary(lib_file)
        library_loaded = True
        print(f"成功加载动态库: {lib_file}")
        break
    except OSError:
        continue

if not library_loaded:
    print("警告：无法加载动态库")
    print("尝试过的文件:")
    for lib_file in library_files:
        print(f"  - {lib_file}")
    print("\n请选择运行模式:")
    print("1. 串口通讯模式（使用真实硬件）")
    print("2. 模拟模式（用于测试）")
    
    while True:
        try:
            choice = input("请输入选择 (1/2): ").strip()
            if choice == '1':
                use_serial_mode = True
                break
            elif choice == '2':
                use_serial_mode = False
                break
            else:
                print("请输入1或2")
        except KeyboardInterrupt:
            print("\n程序退出")
            sys.exit(0)

# 功能列表
functions = ["退出", "设置传感器系数", "查看温补状态", "设置温补开关", "计算温度", 
            "计算压力", "压力校零", "温度校N", "查询设备版本", "查询环境压力", 
            "查询芯片系数", "实时腔长数据"]

def print_error(msg):
    """打印错误信息"""
    print(f'xxxxxxxxxx{msg}xxxxxxxxxx')
    sys.stdout.flush()

def print_hint(msg):
    """打印提示信息"""
    print(f'----------{msg}----------')
    sys.stdout.flush()

def print_success(msg):
    """打印成功信息"""
    print(f'✓✓✓✓✓✓✓✓✓✓{msg}✓✓✓✓✓✓✓✓✓✓')
    sys.stdout.flush()

def info_print():
    """显示功能界面"""
    print_hint('----------')
    print_hint('请选择功能')
    print_hint('----------')
    
    for i in range(1, len(functions)):
        print(f'{i}、{functions[i]}')
    print('0、退出')
    print_hint('----------')

def init_serial_communication():
    """初始化串口通讯"""
    global serial_comm
    
    if not use_serial_mode:
        return False
    
    try:
        serial_comm = JR3220Serial(debug=True)
        
        # 列出可用串口
        available_ports = serial_comm.list_available_ports()
        if not available_ports:
            print_error("未找到可用的串口")
            return False
        
        print("可用串口:")
        for i, port in enumerate(available_ports):
            print(f"{i+1}. {port}")
        
        # 让用户选择串口
        while True:
            try:
                choice = input(f"请选择串口 (1-{len(available_ports)}) 或按回车使用默认: ").strip()
                if not choice:
                    selected_port = available_ports[0]
                    break
                else:
                    idx = int(choice) - 1
                    if 0 <= idx < len(available_ports):
                        selected_port = available_ports[idx]
                        break
                    else:
                        print("选择超出范围")
            except ValueError:
                print("请输入有效数字")
            except KeyboardInterrupt:
                return False
        
        # 连接串口
        if serial_comm.connect(selected_port):
            print_success(f"成功连接到串口: {selected_port}")
            return True
        else:
            print_error("串口连接失败")
            return False
            
    except Exception as e:
        print_error(f"初始化串口通讯失败: {e}")
        return False

def setup_mock_library():
    """设置模拟库"""
    global libx120
    
    class MockLibrary:
        pass
    
    libx120 = MockLibrary()

    def mock_set_sensor_coefficients(data):
        print("模拟：设置传感器系数")
        return True

    def mock_is_temperature_compensation_enabled():
        print("模拟：检查温补状态")
        return True

    def mock_enable_temperature_compensation(enable):
        print(f"模拟：{'开启' if enable else '关闭'}温补")

    def mock_compute_temperature(cl1):
        print(f"模拟：计算温度，输入腔长={cl1}")
        return 25.5

    def mock_compute_pressure(cl1, cl2):
        print(f"模拟：计算压力，输入腔长1={cl1}, 腔长2={cl2}")
        return 101325.0

    def mock_calibrate_pressure(cl1, cl2):
        print(f"模拟：压力校零，输入腔长1={cl1}, 腔长2={cl2}")
        return True

    def mock_calibrate_temperature(temp, cl1):
        print(f"模拟：温度校N，输入温度={temp}, 腔长1={cl1}")
        return True

    # 设置模拟函数
    libx120.set_sensor_coefficients = mock_set_sensor_coefficients
    libx120.is_temperature_compensation_enabled = mock_is_temperature_compensation_enabled
    libx120.enable_temperature_compensation = mock_enable_temperature_compensation
    libx120.compute_temperature = mock_compute_temperature
    libx120.compute_pressure = mock_compute_pressure
    libx120.calibrate_pressure = mock_calibrate_pressure
    libx120.calibrate_temperature = mock_calibrate_temperature

    # 为模拟函数添加属性（模拟ctypes函数的属性）
    for func_name in ['set_sensor_coefficients', 'is_temperature_compensation_enabled',
                      'enable_temperature_compensation', 'compute_temperature',
                      'compute_pressure', 'calibrate_pressure', 'calibrate_temperature']:
        func = getattr(libx120, func_name)
        func.argtypes = None
        func.restype = None

    print("已启用模拟模式")

# 根据模式初始化
if use_serial_mode:
    if not init_serial_communication():
        print("串口通讯初始化失败，切换到模拟模式")
        use_serial_mode = False
        setup_mock_library()
else:
    setup_mock_library()

def main():
    """主程序"""
    print_hint("X120传感器控制程序 - 增强版")
    if use_serial_mode and serial_comm:
        print_hint("串口通讯模式已启用")
    else:
        print_hint("模拟模式已启用")
    print_hint("程序启动成功")
    
    while True:
        try:
            info_print()
            no = int(input("输入功能序号："))
            
            if no == 1:
                call_set_sensor_coefficients(1)
            elif no == 2:
                call_is_temperature_compensation_enabled(2)
            elif no == 3:
                call_enable_temperature_compensation(3)
            elif no == 4:
                call_compute_temperature(4)
            elif no == 5:
                call_compute_pressure(5)
            elif no == 6:
                call_calibrate_pressure(6)
            elif no == 7:
                call_calibrate_temperature(7)
            elif no == 8:
                call_query_device_version(8)
            elif no == 9:
                call_query_environmental_pressure(9)
            elif no == 10:
                call_query_chip_coefficient(10)
            elif no == 11:
                call_real_time_cavity_data(11)
            elif no == 0:
                print_hint("退出程序")
                break
            else:
                print_error("无效功能序号")
                
        except ValueError:
            print_error("请输入有效的数字")
        except KeyboardInterrupt:
            print_hint("\n程序被用户中断")
            break
        except Exception as e:
            print_error(f"程序运行出错: {e}")
            
        # 添加分隔线，便于查看
        print()
    
    # 清理资源
    if serial_comm:
        serial_comm.disconnect()

# ==================== 功能函数实现 ====================

def call_set_sensor_coefficients(no):
    """设置传感器系数"""
    print_hint(functions[no])
    try:
        data = input("输入传感器系数：")
        if not data.strip():
            print_error("传感器系数不能为空")
            return

        if use_serial_mode and serial_comm:
            # 串口模式下，传感器系数通常存储在芯片中，这里可以扩展
            print_hint("串口模式下传感器系数存储在芯片中")
            print_hint("请使用查询芯片系数功能查看当前系数")
        else:
            # 模拟模式
            c_str = c_char_p(data.encode('utf-8'))
            libx120.set_sensor_coefficients.argtypes = [c_char_p]
            libx120.set_sensor_coefficients.restype = c_bool
            flag = libx120.set_sensor_coefficients(c_str)
            if flag:
                print_success('设置系数成功')
            else:
                print_error('设置系数失败')
    except Exception as e:
        print_error(f'设置传感器系数失败: {e}')

def call_is_temperature_compensation_enabled(no):
    """读取压力温度补偿开关的状态"""
    print_hint(functions[no])
    try:
        if use_serial_mode and serial_comm:
            # 串口模式下，温补状态需要通过其他方式查询
            print_hint("串口模式下温补状态查询功能待实现")
        else:
            # 模拟模式
            libx120.is_temperature_compensation_enabled.restype = c_bool
            flag = libx120.is_temperature_compensation_enabled()
            if flag:
                print_success("温补已开启")
            else:
                print_hint("温补已关闭")
    except Exception as e:
        print_error(f"读取温补状态失败: {e}")

def call_enable_temperature_compensation(no):
    """设置压力温度补偿开关"""
    print_hint(functions[no])
    try:
        choice = int(input("输入序号（0-关闭温补，1-开启温补）："))

        if use_serial_mode and serial_comm:
            # 串口模式下，温补开关需要通过其他方式设置
            print_hint("串口模式下温补开关设置功能待实现")
        else:
            # 模拟模式
            libx120.enable_temperature_compensation.argtypes = [c_bool]
            libx120.enable_temperature_compensation.restype = None
            if choice == 0:
                libx120.enable_temperature_compensation(False)
                print_success('关闭温补完成')
            elif choice == 1:
                libx120.enable_temperature_compensation(True)
                print_success('开启温补完成')
            else:
                print_error("序号无效")
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f"设置温补开关失败: {e}")

def call_compute_temperature(no):
    """计算温度"""
    print_hint(functions[no])
    try:
        cl1 = float(input("输入通道1腔长："))

        if use_serial_mode and serial_comm:
            # 串口模式下，温度计算基于实时腔长数据
            print_hint("串口模式：基于实时腔长数据计算温度")
            print_hint("请使用实时腔长数据功能获取当前腔长")
            # 这里可以扩展实际的温度计算算法
            print_hint(f"输入腔长: {cl1} nm")
        else:
            # 模拟模式
            libx120.compute_temperature.argtypes = [c_float]
            libx120.compute_temperature.restype = c_float
            temp = libx120.compute_temperature(cl1)
            print_success(f'计算温度：{temp:.2f}°C')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'计算温度失败: {e}')

def call_compute_pressure(no):
    """计算压力"""
    print_hint(functions[no])
    try:
        cl1 = float(input("输入通道1腔长："))
        cl2 = float(input("输入通道2腔长："))

        if use_serial_mode and serial_comm:
            # 串口模式下，压力计算基于实时腔长数据
            print_hint("串口模式：基于实时腔长数据计算压力")
            print_hint("请使用实时腔长数据功能获取当前腔长")
            print_hint(f"输入腔长1: {cl1} nm, 腔长2: {cl2} nm")
        else:
            # 模拟模式
            libx120.compute_pressure.argtypes = [c_float, c_float]
            libx120.compute_pressure.restype = c_float
            pressure = libx120.compute_pressure(cl1, cl2)
            print_success(f'计算压力：{pressure:.2f} Pa')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'计算压力失败: {e}')

def call_calibrate_pressure(no):
    """压力校零"""
    print_hint(functions[no])
    try:
        cl1 = float(input("输入通道1腔长："))
        cl2 = float(input("输入通道2腔长："))

        if use_serial_mode and serial_comm:
            print_hint("串口模式：压力校零功能")
            print_hint(f"校零腔长1: {cl1} nm, 腔长2: {cl2} nm")
            print_hint("校零操作需要结合具体的校准算法")
        else:
            # 模拟模式
            libx120.calibrate_pressure.argtypes = [c_float, c_float]
            libx120.calibrate_pressure.restype = c_bool
            flag = libx120.calibrate_pressure(cl1, cl2)
            if flag:
                print_success('压力校零成功')
            else:
                print_error('压力校零失败')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'压力校零失败: {e}')

def call_calibrate_temperature(no):
    """温度校N"""
    print_hint(functions[no])
    try:
        temp = float(input("输入校零温度："))
        cl1 = float(input("输入通道1腔长："))

        if use_serial_mode and serial_comm:
            print_hint("串口模式：温度校N功能")
            print_hint(f"校零温度: {temp}°C, 腔长1: {cl1} nm")
            print_hint("校零操作需要结合具体的校准算法")
        else:
            # 模拟模式
            libx120.calibrate_temperature.argtypes = [c_float, c_float]
            libx120.calibrate_temperature.restype = c_bool
            flag = libx120.calibrate_temperature(temp, cl1)
            if flag:
                print_success('温度校N成功')
            else:
                print_error('温度校N失败')
    except ValueError:
        print_error("请输入有效的数字")
    except Exception as e:
        print_error(f'温度校N失败: {e}')

def call_query_device_version(no):
    """查询设备版本"""
    print_hint(functions[no])
    try:
        if use_serial_mode and serial_comm:
            version = serial_comm.query_version()
            if version:
                print_success(f'设备版本: {version}')
            else:
                print_error('查询设备版本失败')
        else:
            print_hint("模拟模式：设备版本 v1.0.0")
    except Exception as e:
        print_error(f'查询设备版本失败: {e}')

def call_query_environmental_pressure(no):
    """查询环境压力"""
    print_hint(functions[no])
    try:
        if use_serial_mode and serial_comm:
            pressure = serial_comm.query_environmental_pressure()
            if pressure is not None:
                print_success(f'环境压力: {pressure:.1f} Pa')
                # 转换为其他常用单位
                pressure_mmhg = pressure * 7.5e-3
                pressure_kpa = pressure / 1000.0
                print_hint(f'环境压力: {pressure_mmhg:.2f} mmHg')
                print_hint(f'环境压力: {pressure_kpa:.2f} kPa')
            else:
                print_error('查询环境压力失败')
        else:
            print_hint("模拟模式：环境压力 101325.0 Pa (1个标准大气压)")
    except Exception as e:
        print_error(f'查询环境压力失败: {e}')

def call_query_chip_coefficient(no):
    """查询芯片系数"""
    print_hint(functions[no])
    try:
        if use_serial_mode and serial_comm:
            coeff_info = serial_comm.query_chip_coefficient()
            if coeff_info:
                if coeff_info.get('status') == 'no_chip':
                    print_hint('未检测到系数芯片')
                elif coeff_info.get('status') == 'success':
                    print_success('成功读取芯片系数')
                    print_hint(f'系数内容: {coeff_info["coefficient_text"]}')
                    if coeff_info['crc_valid']:
                        print_success('CRC校验通过')
                    else:
                        print_error('CRC校验失败')
                        print_hint(f'计算CRC: {coeff_info["calculated_crc"]:08X}')
                        print_hint(f'接收CRC: {coeff_info["received_crc"]:08X}')
                else:
                    print_error('读取芯片系数失败')
            else:
                print_error('查询芯片系数失败')
        else:
            print_hint("模拟模式：芯片系数 - 模拟传感器系数")
    except Exception as e:
        print_error(f'查询芯片系数失败: {e}')

def call_real_time_cavity_data(no):
    """实时腔长数据"""
    print_hint(functions[no])
    try:
        if use_serial_mode and serial_comm:
            duration = float(input("输入数据采集时间（秒）[默认10秒]: ") or "10")
            print_hint(f'开始采集腔长数据，持续时间: {duration}秒')
            print_hint('按 Ctrl+C 可提前停止采集')

            cavity_data_list = serial_comm.read_cavity_data_stream(duration)

            if cavity_data_list:
                print_success(f'成功采集到 {len(cavity_data_list)} 组数据')
                print_hint('最近5组数据:')
                for i, data in enumerate(cavity_data_list[-5:]):
                    timestamp = time.strftime('%H:%M:%S', time.localtime(data['timestamp']))
                    print(f'{timestamp} - 温度通道: {data["temperature_cavity"]:.3f}nm, '
                          f'压力通道: {data["pressure_cavity"]:.3f}nm')

                # 询问是否保存数据
                save_choice = input("是否保存数据到文件? (y/n): ").strip().lower()
                if save_choice == 'y':
                    filename = f'cavity_data_{int(time.time())}.txt'
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write('时间戳,温度通道腔长(nm),压力通道腔长(nm)\n')
                        for data in cavity_data_list:
                            timestamp = time.strftime('%Y-%m-%d %H:%M:%S',
                                                    time.localtime(data['timestamp']))
                            f.write(f'{timestamp},{data["temperature_cavity"]:.3f},'
                                   f'{data["pressure_cavity"]:.3f}\n')
                    print_success(f'数据已保存到: {filename}')
            else:
                print_error('未采集到有效数据')
        else:
            print_hint("模拟模式：实时腔长数据")
            print_hint("温度通道: 18222.199 nm")
            print_hint("压力通道: 18221.211 nm")
    except ValueError:
        print_error("请输入有效的数字")
    except KeyboardInterrupt:
        print_hint("\n数据采集被用户中断")
    except Exception as e:
        print_error(f'实时腔长数据采集失败: {e}')

if __name__ == "__main__":
    main()
